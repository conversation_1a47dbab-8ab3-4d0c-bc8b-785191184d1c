#!/bin/bash

# ZhuLinks API 启动脚本
# 使用 Gunicorn + Uvicorn 部署

set -e  # 遇到错误立即退出

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== ZhuLinks API 启动脚本 ===${NC}"

# 激活虚拟环境（如果使用）
if [ -d "venv" ]; then
    echo -e "${GREEN}激活虚拟环境...${NC}"
    source venv/bin/activate
elif [ -d ".venv" ]; then
    echo -e "${GREEN}激活虚拟环境...${NC}"
    source .venv/bin/activate
fi

# 检查依赖
echo -e "${GREEN}检查依赖...${NC}"
python -c "import gunicorn, uvicorn" || {
    echo "缺少依赖，正在安装..."
    pip install gunicorn uvicorn[standard]
}

# 创建日志目录
mkdir -p logs

# 启动应用
echo -e "${GREEN}启动 Gunicorn + Uvicorn 服务器...${NC}"
echo "配置文件: gunicorn.conf.py"
echo "应用模块: app.main:app"
echo "监听地址: http://0.0.0.0:8000"
echo ""

gunicorn -c gunicorn.conf.py app.main:app
