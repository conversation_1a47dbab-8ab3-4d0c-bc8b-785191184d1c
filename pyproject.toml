[project]
name = "zhulinks-cert"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "aerich>=0.9.0",
    "aioredis>=2.0.1",
    "alibabacloud-dysmsapi20170525>=4.1.1",
    "argon2-cffi>=25.1.0",
    "asyncpg>=0.30.0",
    "bcrypt==4.0.1",
    "fastapi>=0.115.12",
    "fastapi-pagination>=0.13.2",
    "gunicorn>=23.0.0",
    "httpx>=0.28.1",
    "loguru>=0.7.3",
    "passlib>=1.7.4",
    "pydantic-settings>=2.9.1",
    "pytest>=8.4.1",
    "pytest-asyncio>=1.0.0",
    "python-jose>=3.5.0",
    "python-multipart>=0.0.20",
    "redis>=6.2.0",
    "setuptools>=80.9.0",
    "tomlkit>=0.13.3",
    "tortoise-orm>=0.25.1",
    "uvicorn>=0.34.3",
]

[[tool.uv.index]]
#url = "https://pypi.tuna.tsinghua.edu.cn/simple"
url = "https://mirrors.aliyun.com/pypi/simple"
default = true

[tool.ruff]
target-version = "py310"
exclude = ["aerich"]
line-length = 180

[tool.aerich]
tortoise_orm = "app.core.tortoise_config.TORTOISE_ORM"
location = "./migrations"
src_folder = "./."

[dependency-groups]
dev = ["ruff>=0.11.13"]
