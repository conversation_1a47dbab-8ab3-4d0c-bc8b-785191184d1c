#!/usr/bin/env python3
"""
测试 inspectdb 脚本
"""

import subprocess
import sys
from pathlib import Path

def test_inspectdb():
    """测试 inspectdb 脚本"""
    print("🧪 测试 inspectdb 脚本...")
    
    # 检查脚本是否存在
    script_path = Path("scripts/inspectdb.py")
    if not script_path.exists():
        print("❌ 脚本文件不存在")
        return False
    
    print("✅ 脚本文件存在")
    
    # 尝试运行脚本（会因为需要用户输入而停止，但可以检查导入是否正常）
    try:
        # 只检查脚本的语法和导入
        result = subprocess.run([
            sys.executable, "-c", 
            "import sys; sys.path.append('.'); "
            "from scripts.inspectdb import get_user_input, create_temp_config; "
            "print('✅ 脚本导入成功')"
        ], capture_output=True, text=True, timeout=5)
        
        if result.returncode == 0:
            print("✅ 脚本导入测试通过")
            print(result.stdout)
            return True
        else:
            print("❌ 脚本导入测试失败")
            print(f"错误: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️  脚本运行超时（这是正常的，因为需要用户输入）")
        return True
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False

if __name__ == "__main__":
    success = test_inspectdb()
    if success:
        print("\n🎉 测试通过！")
        print("您现在可以运行: python scripts/inspectdb.py")
    else:
        print("\n💥 测试失败！")
        sys.exit(1)
