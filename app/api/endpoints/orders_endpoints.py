# -*- coding: utf-8 -*-
from typing import Annotated

from fastapi import Depends, Query
from fastapi.routing import APIRouter
from fastapi_pagination import Page

from app.api.dependencies import get_current_user
from app.filters.order_filters import OrderFilterQueryParams
from app.models import User
from app.schemas.base import RestfulResponse
from app.schemas.order_schemas import OrderInfoResponse
from app.services.order_service import OrderService

orders_router = APIRouter()


@orders_router.get("", summary="获取订单列表", response_model=RestfulResponse[Page[OrderInfoResponse]])
async def get_orders(filter_query: Annotated[OrderFilterQueryParams, Query()], current_user: User = Depends(get_current_user)):
    service = OrderService()
    data = await service.get_order_with_raw_sql(current_user, filter_query)
    return RestfulResponse(data=data)
