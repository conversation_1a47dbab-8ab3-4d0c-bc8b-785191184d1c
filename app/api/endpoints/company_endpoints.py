from typing import Annotated, List

from fastapi import APIRouter, Depends, Query
from fastapi_pagination import Page
from fastapi_pagination.ext.tortoise import apaginate

from app.api.dependencies import get_current_user
from app.core.exceptions import BusinessException, PermissionException
from app.filters.company_filters import CompanyFilterQueryParams
from app.models.company_models import Company, CompanyPydantic
from app.models.user_models import User
from app.schemas.base import RestfulResponse
from app.schemas.company_schemas import CompanyCreate, CompanyEnumResponse, CompanyUpdate

company_router = APIRouter()


@company_router.get(
    "",
    response_model=RestfulResponse[Page[CompanyPydantic]],
    summary="获取公司列表",
)
async def get_companies(filter_query: Annotated[CompanyFilterQueryParams, Query()], current_user: User = Depends(get_current_user)):
    """
    获取公司列表
    """
    query = Company.filter(is_deleted=False)
    if not current_user.is_superuser:
        query = query.filter(id=current_user.company_id)

    paginate_data = await apaginate(query, filter_query)
    return RestfulResponse(data=paginate_data)


@company_router.get(
    "/enum",
    response_model=RestfulResponse[List[CompanyEnumResponse]],
    summary="获取公司枚举",
)
async def get_company_enum(current_user: User = Depends(get_current_user)):
    """
    获取公司枚举
    """
    query = Company.filter(is_deleted=False)
    if not current_user.is_superuser:
        query = query.filter(id=current_user.company_id)

    data = [CompanyEnumResponse(company_id=i.company_id, name=i.name) for i in await query.only("company_id", "name")]
    return RestfulResponse(data=data)


@company_router.get(
    "/{company_id}",
    response_model=RestfulResponse[CompanyPydantic],
    summary="获取公司详情",
)
async def get_company(company_id: str, current_user: User = Depends(get_current_user)):
    """
    获取指定公司详情
    """
    company = await Company.get_or_none(company_id=company_id, is_deleted=False)
    if not company:
        raise BusinessException(code=404, msg="公司不存在")

    return RestfulResponse(data=await CompanyPydantic.from_tortoise_orm(company))


@company_router.post(
    "",
    response_model=RestfulResponse[CompanyPydantic],
    summary="创建公司",
)
async def create_company(company_data: CompanyCreate, current_user: User = Depends(get_current_user)):
    """
    创建新公司
    """
    # 只有超级用户可以创建公司
    if not current_user.is_superuser:
        raise PermissionException

    company_obj = await Company.create(
        **company_data.model_dump(),
        create_user_id=current_user.id,
    )
    data = await CompanyPydantic.from_tortoise_orm(company_obj)
    return RestfulResponse(data=data)


@company_router.put(
    "/{company_id}",
    response_model=RestfulResponse[CompanyPydantic],
    summary="更新公司信息",
)
async def update_company(company_id: str, update_data: CompanyUpdate, current_user: User = Depends(get_current_user)):
    """
    更新公司信息
    """
    # 只有超级用户可以更新公司信息
    if not current_user.is_superuser:
        raise PermissionException

    existing_company = await Company.get_or_none(company_id=company_id, is_deleted=False)
    if not existing_company:
        raise BusinessException(code=404, msg="公司不存在")

    # 更新公司信息
    await existing_company.update_from_dict(update_data.model_dump(exclude_unset=True))
    existing_company.update_user_id = current_user.id
    await existing_company.save()
    return RestfulResponse(data=await CompanyPydantic.from_tortoise_orm(existing_company))


@company_router.delete(
    "/{company_id}",
    response_model=RestfulResponse[None],
    summary="删除公司",
)
async def delete_company(company_id: str, current_user: User = Depends(get_current_user)):
    """
    删除公司
    """
    # 只有超级用户可以删除公司
    if not current_user.is_superuser:
        raise PermissionException

    company = await Company.get_or_none(company_id=company_id, is_deleted=False)
    if not company:
        raise BusinessException(code=404, msg="公司不存在")

    company.is_deleted = True
    company.update_user_id = current_user.id
    await company.save()
    return RestfulResponse()
