from typing import Any, Dict

from fastapi import APIRouter, Body, Query
from loguru import logger

from app.services.subscribe_service import SubscribeService

subscribe_router = APIRouter()


@subscribe_router.post(
    "/shipping_subscribe",
    tags=["订阅管理"],
    response_model=Dict[str, Any],
    summary="发货订阅",
)
async def shipping_subscribe(
    state: str = Query("JST", description="消息来源:JST(聚水潭)/DD(抖店)"),
    body: Any = Body(..., description="消息体数据,暂时只支持JST的订阅信息"),
):
    try:
        # 测试链接是否成功
        if state == "JST" and isinstance(body, bytes):
            return {"code": 0, "msg": "执行成功"}

        if state == "JST":
            ret = await SubscribeService.create_jst_shipping_subscribe(body)
            return ret
        return {"code": 0, "msg": "success"}
    except Exception as e:
        logger.warning(f"发货订阅失败: {e}, {body}")
        return {"code": -1, "msg": "执行失败"}
