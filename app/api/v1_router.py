# -*- coding: utf-8 -*-
from fastapi import APIRouter

from app.api.endpoints import (
    common_router,
    company_router,
    inbound_order_detail_router,
    inbound_order_router,
    login_router,
    orders_router,
    shipping_router,
    subscribe_router,
    user_router,
)

api_router = APIRouter(redirect_slashes=False)

api_router.include_router(login_router, prefix="/auth", tags=["登录接口"])
api_router.include_router(user_router, prefix="/users", tags=["用户管理"])
api_router.include_router(company_router, prefix="/companies", tags=["公司管理"])
api_router.include_router(subscribe_router, prefix="/subscribe", tags=["订阅管理"])
api_router.include_router(inbound_order_router, prefix="/inbound-order", tags=["入库记录"])
api_router.include_router(inbound_order_detail_router, prefix="/inbound-order/{code}/details", tags=["入库记录-清点明细"])
api_router.include_router(shipping_router, prefix="/shipping", tags=["发货管理"])
api_router.include_router(orders_router, prefix="/orders", tags=["订单管理"])
api_router.include_router(common_router, prefix="/common")
# api_router.include_router(order_test_router, prefix="/test", tags=["测试接口"])
