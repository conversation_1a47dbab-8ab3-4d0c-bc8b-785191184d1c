# -*- coding: utf-8 -*-
import traceback
from typing import Any, Dict

from loguru import logger
from tortoise import connections

from app.core.exceptions import BusinessException
from app.filters.order_filters import OrderFilterQueryParams
from app.models import LiveAuthor, Shops, User


class OrderService:
    @staticmethod
    async def get_order_with_raw_sql(current_user: User, filter_query: OrderFilterQueryParams) -> Dict[str, Any]:
        """
        使用原生SQL获取订单数据，支持分页

        Args:
            current_user: 当前用户
        Returns:
            包含分页信息和数据的字典
            :param filter_query:
        """
        page = filter_query.page
        size = filter_query.size

        if not page or page < 1:
            page = 1
        if not size or size < 1 or size > 1000:
            size = 20

        offset = (page - 1) * size

        # 构建WHERE条件
        where_conditions = ["wp.is_deleted = false", "wp.company_id = $1", "iod.shipping_user_id = $2"]
        params = [current_user.company_id, current_user.id]
        param_index = 3

        if filter_query.order_id:
            where_conditions.append(f"wp.order_id like ${param_index}")
            params.append(f"%{filter_query.order_id}%")
            param_index += 1

        if filter_query.cert_sn_code:
            where_conditions.append(f"wp.cert_sn_code like ${param_index}")
            params.append(f"%{filter_query.cert_sn_code}%")
            param_index += 1

        if filter_query.product_sn_code:
            where_conditions.append(f"wp.product_sn_code like ${param_index}")
            params.append(f"%{filter_query.product_sn_code}%")
            param_index += 1

        if filter_query.erp_order_id:
            where_conditions.append(f"oo.order_id like ${param_index}")
            params.append(f"%{filter_query.erp_order_id}%")
            param_index += 1

        if filter_query.author_id:
            where_conditions.append(f"ooi.author_id = ${param_index}")
            params.append(filter_query.author_id)
            param_index += 1

        if filter_query.shop_id:
            where_conditions.append(f"oo.shop_id = ${param_index}")
            params.append(filter_query.shop_id)
            param_index += 1

        if filter_query.order_time_start:
            where_conditions.append(f"oo.order_time >= ${param_index}")
            params.append(filter_query.order_time_start)
            param_index += 1

        if filter_query.order_time_end:
            where_conditions.append(f"oo.order_time <= ${param_index}")
            params.append(filter_query.order_time_end)
            param_index += 1

        if filter_query.pay_time_start:
            where_conditions.append(f"oo.pay_time >= ${param_index}")
            params.append(filter_query.pay_time_start)
            param_index += 1

        if filter_query.pay_time_end:
            where_conditions.append(f"oo.pay_time <= ${param_index}")
            params.append(filter_query.pay_time_end)
            param_index += 1

        if filter_query.send_date_start:
            where_conditions.append(f"wp.send_date >= ${param_index}")
            params.append(filter_query.send_date_start)
            param_index += 1

        if filter_query.send_date_end:
            where_conditions.append(f"wp.send_date <= ${param_index}")
            params.append(filter_query.send_date_end)
            param_index += 1

        where_clause = " AND ".join(where_conditions)

        try:
            # 获取数据库连接

            conn = connections.get("default")

            # 构建主查询SQL - 使用LEFT JOIN关联订单数据
            main_sql = f"""
            SELECT
                wp.id,
                wp.waybill_code,
                wp.order_id,
                wp.outer_oi_id,
                wp.sku_id,

                wp.quantity,
                wp.bind_status,
                wp.create_time,
                wp.send_date,
                wp.cert_sn_code,
                wp.product_sn_code,
                wp.cert_link,

                -- 来自OrdersOrder的字段
                oo.order_time,
                oo.pay_time,
                oo.shop_id,
                oo.order_id as erp_order_id,
                
                -- 来自OrdersOrderitems的字段
                ooi.author_id as author_id,
                ooi.order_status as order_status,
                ooi.order_status_desc as order_status_desc,
                ooi.raw_spec_code as spec_code,
                ooi.raw_product_name as product_name,
                ooi.raw_product_pic as product_image
                
            FROM cert_waybill_product wp
            LEFT JOIN cert_inbound_order_detail iod ON wp.inbound_order_detail_id = iod.id
            LEFT JOIN orders_order oo ON wp.order_id = oo.ex_order_id
            LEFT JOIN orders_orderitems ooi ON wp.outer_oi_id = ooi.ex_sub_order_id
            WHERE {where_clause}
            ORDER BY wp.bind_status ASC, wp.create_time ASC
            LIMIT ${param_index} OFFSET ${param_index + 1}
            """

            # 添加分页参数
            params.extend([size, offset])

            # 构建计数查询SQL
            count_sql = f"""
            SELECT COUNT(*) as total_count
            FROM cert_waybill_product wp
            LEFT JOIN cert_inbound_order_detail iod ON wp.inbound_order_detail_id = iod.id
            WHERE {where_clause}
            """

            # 执行计数查询
            count_params = params[:-2]  # 移除LIMIT和OFFSET参数
            count_result = await conn.execute_query(count_sql, count_params)

            total_count = count_result[1][0]["total_count"] if count_result and count_result[1] else 0

            # 执行主查询
            main_result = await conn.execute_query(main_sql, params)

            # 处理查询结果
            data = [{column_name: value for column_name, value in i.items()} for i in main_result[1]]

            # 批量查询店铺信息
            shop_ids = list(set([item["shop_id"] for item in data]))
            shops = await Shops.filter(id__in=shop_ids).all()

            shop_map = {shop.id: shop for shop in shops}

            # 批量查询达人信息
            author_ids = list(set([item["author_id"] for item in data]))
            authors = await LiveAuthor.filter(id__in=author_ids).all()
            author_map = {author.id: author for author in authors}

            for item in data:
                item["shop_name"] = shop_map.get(item["shop_id"], {}).get("shop_name")
                item["author_name"] = author_map.get(item["author_id"], {}).get("author_name")

            # 计算分页信息
            total_pages = (total_count + size - 1) // size

            return {
                "total": total_count,
                "page": page,
                "size": size,
                "pages": total_pages,
                "items": data,
            }

        except Exception as e:
            logger.error(f"获取订单数据失败: {e}, {traceback.format_exc()}")

            raise BusinessException(msg="获取订单数据失败")
