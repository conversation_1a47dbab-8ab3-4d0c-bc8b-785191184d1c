from fastapi_pagination.ext.tortoise import apaginate

from app.core.exceptions import BusinessException
from app.filters.common_filters import LiveAuthFilterParams
from app.models.common_models import LiveAuthor
from app.models.user_models import User
from app.schemas.live_author_schemas import BulkCreateLiveAuthorPydantic, LiveAuthorPydantic


class LiveAuthService:
    @staticmethod
    async def create_live_auth(create_data: LiveAuthorPydantic, current_user: User):
        if await LiveAuthor.get_or_none(author_id=create_data.author_id):
            raise BusinessException(msg="达人id已存在")

        live_auth = LiveAuthor(
            author_id=create_data.author_id,
            author_name=create_data.author_name,
            company_id=current_user.company_id,
            create_user_id=current_user.id,
        )
        await live_auth.save()
        return live_auth

    @staticmethod
    async def get_live_auth_list(filter_query: LiveAuthFilterParams, current_user: User):
        # 加分页
        live_auths = LiveAuthor.filter(company_id=current_user.company_id).all()

        live_auths = await apaginate(live_auths, filter_query)

        return live_auths

    @staticmethod
    async def get_live_auth_enum(current_user: User) -> list[LiveAuthor]:
        return await LiveAuthor.filter(company_id=current_user.company_id, is_deleted=False).all()

    @staticmethod
    async def update_live_auth(author_id: str, update_data: LiveAuthorPydantic, current_user: User):
        live_auth = await LiveAuthor.get_or_none(author_id=author_id)
        if not live_auth:
            raise BusinessException(msg="达人不存在")

        live_auth.author_name = update_data.author_name
        live_auth.update_user_id = current_user.id
        await live_auth.save()
        return await LiveAuthorPydantic.from_tortoise_orm(live_auth)

    @staticmethod
    async def delete_live_auth(author_id: str, current_user: User):
        live_auth = await LiveAuthor.get_or_none(author_id=author_id)
        if not live_auth:
            raise BusinessException(msg="达人不存在")

        live_auth.is_deleted = True
        live_auth.update_user_id = current_user.id
        await live_auth.save()

    @staticmethod
    async def batch_create_live_auth(create_live_auths_data: BulkCreateLiveAuthorPydantic, current_user: User):
        if not current_user.company_id:
            raise BusinessException(msg="请先绑定用户公司")

        # 批量查询达人id是否存在，在的话就走更新逻辑，不在的话就走创建逻辑
        author_ids = [user.author_id for user in create_live_auths_data.live_auths]
        exist_live_auths = await LiveAuthor.filter(author_id__in=author_ids).all()
        exist_author_ids = [user.author_id for user in exist_live_auths]
        # 批量创建达人 或者 批量更新达人 bulk_create/bulk_update
        need_update_live_auths = []
        need_create_live_auths = []
        for create_live_auth in create_live_auths_data.live_auths:
            if create_live_auth.author_id in exist_author_ids:
                create_live_auth.author_name = create_live_auth.author_name
                create_live_auth.update_user_id = current_user.id
                need_update_live_auths.append(create_live_auth)
            else:
                need_create_live_auths.append(
                    LiveAuthor(
                        author_id=create_live_auth.author_id,
                        author_name=create_live_auth.author_name,
                        company_id=current_user.company_id,
                        create_user_id=current_user.id,
                    )
                )

        if need_update_live_auths:
            await LiveAuthor.bulk_update(need_update_live_auths, fields=["author_name", "update_user_id"])
        if need_create_live_auths:
            await LiveAuthor.bulk_create(need_create_live_auths)
        return None
