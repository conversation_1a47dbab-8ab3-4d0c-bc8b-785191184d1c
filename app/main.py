from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi_pagination import add_pagination
from tortoise.contrib.fastapi import register_tortoise

from app.api.v1_router import api_router as v1_api_router
from app.core import logging
from app.core.config import settings
from app.core.redis import redis_lifespan
from app.core.tortoise_config import TORTOISE_ORM
from app.middlewares import register_middlewares

# 初始化日志
logger = logging.setup()
# 获取全局logger
app_logger = logging.get_logger()


@asynccontextmanager
async def app_lifespan(app: FastAPI):
    """
    应用程序生命周期管理器
    """
    import os

    worker_id = os.environ.get("WORKER_PID", os.getpid())
    is_gunicorn = os.environ.get("GUNICORN_WORKER") == "true"

    if is_gunicorn:
        app_logger.info(f"Worker {worker_id}: 应用程序启动中...")
    else:
        app_logger.info("应用程序启动中...")

    try:
        # 启动时初始化 Redis
        async with redis_lifespan(app):
            if is_gunicorn:
                app_logger.info(f"Worker {worker_id}: 应用程序已成功启动")
            else:
                app_logger.info("应用程序已成功启动")
            yield
    finally:
        # 应用关闭时的清理工作
        if is_gunicorn:
            app_logger.debug(f"Worker {worker_id}: 开始应用程序关闭清理...")
        else:
            app_logger.info("开始应用程序关闭清理...")

        # 导入清理函数
        from app.core.logging import cleanup_logging, force_cleanup_multiprocessing

        # 清理日志系统
        try:
            cleanup_logging()
        except Exception as e:
            if not is_gunicorn:
                print(f"清理日志系统时出错: {e}")

        # 清理多进程资源 - 在gunicorn环境下可能会出现预期的错误
        try:
            force_cleanup_multiprocessing()
        except Exception as e:
            if not is_gunicorn:
                print(f"清理多进程资源时出错: {e}")

        if is_gunicorn:
            app_logger.debug(f"Worker {worker_id}: 应用程序已完成关闭")
        else:
            app_logger.info("应用程序已完成关闭")


# 创建 FastAPI 应用
app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    lifespan=app_lifespan,
)


# 注册中间件
register_middlewares(app)
# 配置 CORS
if settings.BACKEND_CORS_ORIGINS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

# 注册路由
app.include_router(v1_api_router, prefix=settings.API_V1_STR)


# 添加分页注册
add_pagination(app)


# 注册 Tortoise ORM
register_tortoise(
    app,
    config=TORTOISE_ORM,
    generate_schemas=True,
    add_exception_handlers=True,
)
