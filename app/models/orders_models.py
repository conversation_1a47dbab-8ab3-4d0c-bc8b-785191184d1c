# -*- coding: utf-8 -*-
# 自动生成的 Tortoise ORM 模型
# 数据库连接: orders
# 生成时间: 2025-06-30 17:09:37

from tortoise import fields, models


class OrdersOrder(models.Model):
    id = fields.BigIntField(primary_key=True, description="id")
    order_id = fields.CharField(max_length=64, description="order_id")
    ex_order_id = fields.CharField(max_length=64, description="ex_order_id")
    order_status = fields.CharField(max_length=6, description="order_status")
    order_status_desc = fields.CharField(max_length=16, null=True, description="order_status_desc")
    main_status = fields.SmallIntField(description="main_status")
    main_status_desc = fields.Char<PERSON>ield(max_length=64, null=True, description="main_status_desc")
    order_type = fields.IntField(description="order_type")
    order_platform = fields.CharField(max_length=6, description="order_platform")
    data_source = fields.Char<PERSON>ield(max_length=3, description="data_source")
    biz = fields.SmallIntField(description="biz")
    order_time = fields.DatetimeField(description="order_time")
    pay_time = fields.DatetimeField(null=True, description="pay_time")
    finish_time = fields.DatetimeField(null=True, description="finish_time")
    order_expire_time = fields.DatetimeField(null=True, description="order_expire_time")
    appointment_ship_time = fields.DatetimeField(null=True, description="appointment_ship_time")
    pay_type = fields.IntField(description="pay_type")
    channel_payment_no = fields.CharField(max_length=255, null=True, description="channel_payment_no")
    b_type = fields.IntField(description="b_type")
    trade_type = fields.IntField(null=True, description="trade_type")
    order_amount = fields.DecimalField(max_digits=10, decimal_places=2, null=True, description="order_amount")
    pay_amount = fields.DecimalField(max_digits=10, decimal_places=2, null=True, description="pay_amount")
    paid_amount = fields.DecimalField(max_digits=10, decimal_places=2, null=True, description="paid_amount")
    cost_price_amount = fields.DecimalField(max_digits=10, decimal_places=2, null=True, description="cost_price_amount")
    post_amount = fields.DecimalField(max_digits=10, decimal_places=2, null=True, description="post_amount")
    post_origin_amount = fields.DecimalField(max_digits=10, decimal_places=2, null=True, description="post_origin_amount")
    tax_amount = fields.DecimalField(max_digits=10, decimal_places=2, null=True, description="tax_amount")
    shop_id = fields.CharField(max_length=64, null=True, description="shop_id")
    distributor_id = fields.IntField(description="distributor_id")
    cancel_reason = fields.CharField(max_length=255, null=True, description="cancel_reason")
    create_user = fields.CharField(max_length=32, null=True, description="create_user")
    update_user = fields.CharField(max_length=32, null=True, description="update_user")
    create_date = fields.DatetimeField(null=True, description="create_date")
    update_date = fields.DatetimeField(null=True, description="update_date")
    parent_id = fields.CharField(max_length=64, null=True, description="parent_id")
    product_match_state = fields.SmallIntField(description="product_match_state")
    delivery_method = fields.CharField(max_length=12, null=True, description="delivery_method")

    class Meta:
        table = "orders_order"
        table_description = "OrdersOrder 表"
        manage = False


class OrdersOrderitems(models.Model):
    id = fields.BigIntField(primary_key=True, description="id")
    order_id = fields.CharField(max_length=64, description="order_id")
    parent_ex_order_id = fields.CharField(max_length=100, null=True, description="parent_ex_order_id")
    ex_sub_order_id = fields.CharField(max_length=64, description="ex_sub_order_id")
    raw_spec_code = fields.CharField(max_length=255, null=True, description="raw_spec_code")
    spec_code = fields.CharField(max_length=255, null=True, description="spec_code")
    letters = fields.CharField(max_length=12, null=True, description="letters")
    code = fields.CharField(max_length=20, null=True, description="code")
    distributor_id = fields.IntField(description="distributor_id")
    company_id = fields.IntField(description="company_id")
    sku_id = fields.IntField(null=True, description="sku_id")
    sku_version_id = fields.BigIntField(null=True, description="sku_version_id")
    product_id = fields.IntField(null=True, description="product_id")
    product_version_id = fields.BigIntField(null=True, description="product_version_id")
    sub_product_id = fields.IntField(null=True, description="sub_product_id")
    sub_product_version_id = fields.BigIntField(null=True, description="sub_product_version_id")
    author_id = fields.CharField(max_length=100, null=True, description="author_id")
    author_name = fields.CharField(max_length=255, null=True, description="author_name")
    goods_price = fields.DecimalField(max_digits=10, decimal_places=2, null=True, description="goods_price")
    goods_type = fields.SmallIntField(null=True, description="goods_type")
    item_num = fields.IntField(null=True, description="item_num")
    product_count = fields.IntField(null=True, description="product_count")
    order_amount = fields.DecimalField(max_digits=10, decimal_places=2, null=True, description="order_amount")
    pay_amount = fields.DecimalField(max_digits=10, decimal_places=2, null=True, description="pay_amount")
    paid_amount = fields.DecimalField(max_digits=10, decimal_places=2, null=True, description="paid_amount")
    cost_price_amount = fields.DecimalField(max_digits=10, decimal_places=2, null=True, description="cost_price_amount")
    post_amount = fields.DecimalField(max_digits=10, decimal_places=2, null=True, description="post_amount")
    exp_ship_time = fields.DatetimeField(null=True, description="exp_ship_time")
    finish_time = fields.DatetimeField(null=True, description="finish_time")
    ship_time = fields.DatetimeField(null=True, description="ship_time")
    confirm_receipt_time = fields.DatetimeField(null=True, description="confirm_receipt_time")
    order_status = fields.CharField(max_length=12, description="order_status")
    order_status_desc = fields.CharField(max_length=16, null=True, description="order_status_desc")
    raw_order_status = fields.CharField(null=True, description="raw_order_status", max_length=100)
    from_mall = fields.BooleanField(null=True, description="from_mall")
    receiver_info = fields.JSONField(null=True, description="receiver_info")
    encrypt_post_receiver = fields.TextField(null=True, description="encrypt_post_receiver")
    encrypt_post_tel = fields.TextField(null=True, description="encrypt_post_tel")
    ad_env_type = fields.CharField(max_length=32, null=True, description="ad_env_type")
    appointment_ship_time = fields.DatetimeField(null=True, description="appointment_ship_time")
    c_biz = fields.IntField(null=True, description="c_biz")
    sub_b_type = fields.IntField(null=True, description="sub_b_type")
    cancel_reason = fields.CharField(max_length=255, null=True, description="cancel_reason")
    channel_payment_no = fields.CharField(max_length=255, null=True, description="channel_payment_no")
    other_info = fields.JSONField(null=True, description="other_info")
    category_id_info = fields.JSONField(null=True, description="category_id_info")
    has_tax = fields.BooleanField(description="has_tax")
    is_comment = fields.SmallIntField(null=True, description="is_comment")
    logistics_receipt_time = fields.DatetimeField(null=True, description="logistics_receipt_time")
    main_status = fields.CharField(max_length=32, null=True, description="main_status")
    main_status_desc = fields.CharField(max_length=255, null=True, description="main_status_desc")
    mask_post_receiver = fields.CharField(max_length=255, null=True, description="mask_post_receiver")
    mask_post_tel = fields.CharField(max_length=255, null=True, description="mask_post_tel")
    modify_amount = fields.DecimalField(max_digits=10, decimal_places=2, null=True, description="modify_amount")
    modify_post_amount = fields.DecimalField(max_digits=10, decimal_places=2, null=True, description="modify_post_amount")
    only_platform_cost_amount = fields.DecimalField(max_digits=10, decimal_places=2, null=True, description="only_platform_cost_amount")
    order_expire_time = fields.DatetimeField(null=True, description="order_expire_time")
    order_level = fields.IntField(description="order_level")
    origin_amount = fields.DecimalField(max_digits=10, decimal_places=2, null=True, description="origin_amount")
    packing_charge_amount = fields.DecimalField(max_digits=10, decimal_places=2, null=True, description="packing_charge_amount")
    platform_cost_amount = fields.DecimalField(max_digits=10, decimal_places=2, null=True, description="platform_cost_amount")
    post_insurance_amount = fields.DecimalField(max_digits=10, decimal_places=2, null=True, description="post_insurance_amount")
    pre_sale_type = fields.SmallIntField(null=True, description="pre_sale_type")
    promotion_amount = fields.DecimalField(max_digits=10, decimal_places=2, null=True, description="promotion_amount")
    promotion_pay_amount = fields.DecimalField(max_digits=10, decimal_places=2, null=True, description="promotion_pay_amount")
    promotion_platform_amount = fields.DecimalField(max_digits=10, decimal_places=2, null=True, description="promotion_platform_amount")
    promotion_redpack_amount = fields.DecimalField(max_digits=10, decimal_places=2, null=True, description="promotion_redpack_amount")
    promotion_redpack_platform_amount = fields.DecimalField(max_digits=10, decimal_places=2, null=True, description="promotion_redpack_platform_amount")
    promotion_redpack_talent_amount = fields.DecimalField(max_digits=10, decimal_places=2, null=True, description="promotion_redpack_talent_amount")
    promotion_shop_amount = fields.DecimalField(max_digits=10, decimal_places=2, null=True, description="promotion_shop_amount")
    promotion_talent_amount = fields.DecimalField(max_digits=10, decimal_places=2, null=True, description="promotion_talent_amount")
    quality_inspection_status = fields.IntField(description="quality_inspection_status")
    receive_type = fields.IntField(null=True, description="receive_type")
    reduce_stock_type = fields.SmallIntField(description="reduce_stock_type")
    shop_cost_amount = fields.DecimalField(max_digits=10, decimal_places=2, null=True, description="shop_cost_amount")
    sku_order_tag_ui = fields.JSONField(null=True, description="sku_order_tag_ui")
    spec = fields.JSONField(null=True, description="spec")
    sum_amount = fields.DecimalField(max_digits=10, decimal_places=2, null=True, description="sum_amount")
    supplier_id = fields.CharField(max_length=255, null=True, description="supplier_id")
    tax_amount = fields.DecimalField(max_digits=10, decimal_places=2, null=True, description="tax_amount")
    theme_type = fields.CharField(null=True, description="theme_type", max_length=100)
    trade_type = fields.IntField(null=True, description="trade_type")
    voucher_deduction_amount = fields.DecimalField(max_digits=10, decimal_places=2, null=True, description="voucher_deduction_amount")
    given_product_activity_info = fields.JSONField(null=True, description="given_product_activity_info")
    after_sale_status = fields.IntField(null=True, description="after_sale_status")
    after_sale_type = fields.SmallIntField(null=True, description="after_sale_type")
    refund_status = fields.SmallIntField(null=True, description="refund_status")
    inventory_list = fields.JSONField(null=True, description="inventory_list")
    logistics_info = fields.JSONField(null=True, description="logistics_info")
    raw_sku_id = fields.CharField(max_length=255, null=True, description="raw_sku_id")
    raw_product_id = fields.CharField(max_length=255, null=True, description="raw_product_id")
    raw_product_name = fields.CharField(max_length=255, null=True, description="raw_product_name")
    raw_product_pic = fields.CharField(max_length=300, null=True, description="raw_product_pic")
    raw_sku_specs = fields.JSONField(null=True, description="raw_sku_specs")
    platform_update_time = fields.DatetimeField(null=True, description="platform_update_time")
    is_gift = fields.BooleanField(description="is_gift")
    create_user = fields.CharField(max_length=32, null=True, description="create_user")
    update_user = fields.CharField(max_length=32, null=True, description="update_user")
    create_date = fields.DatetimeField(null=True, description="create_date")
    update_date = fields.DatetimeField(null=True, description="update_date")
    relate_order_id = fields.CharField(max_length=64, description="relate_order_id")
    order_time = fields.DatetimeField(description="order_time")
    order_type = fields.IntField(description="order_type")
    pay_time = fields.DatetimeField(null=True, description="pay_time")

    class Meta:
        table = "orders_orderitems"
        table_description = "OrdersOrderitems 表"
        manage = False
