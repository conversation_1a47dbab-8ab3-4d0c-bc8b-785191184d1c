from typing import Optional

from pydantic import BaseModel, Field


class CompanyCreate(BaseModel):
    name: str = Field(..., title="公司名称")
    province_id: Optional[str] = Field(None, title="省份ID", nullable=True, max_length=12)
    city_id: Optional[str] = Field(None, title="城市ID", nullable=True, max_length=12)
    district_id: Optional[str] = Field(None, title="区县ID", nullable=True, max_length=12)
    address: Optional[str] = Field(None, title="详细地址", nullable=True)
    contact_user: Optional[str] = Field(None, title="联系人", nullable=True)
    contact_phone: Optional[str] = Field(None, title="联系电话", nullable=True)


class CompanyUpdate(BaseModel):
    name: Optional[str] = Field(None, title="公司名称")
    province_id: Optional[str] = Field(None, title="省份ID", nullable=True, max_length=12)
    city_id: Optional[str] = Field(None, title="城市ID", nullable=True, max_length=12)
    district_id: Optional[str] = Field(None, title="区县ID", nullable=True, max_length=12)
    address: Optional[str] = Field(None, title="详细地址", nullable=True)
    contact_user: Optional[str] = Field(None, title="联系人", nullable=True)
    contact_phone: Optional[str] = Field(None, title="联系电话", nullable=True)


class CompanyEnumResponse(BaseModel):
    company_id: str = Field(..., title="公司ID")
    name: str = Field(..., title="公司名称")
