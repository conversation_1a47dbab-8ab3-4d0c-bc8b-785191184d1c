from contextlib import asynccontextmanager
from typing import Optional

from fastapi import FastAPI
from loguru import logger
from redis.asyncio import ConnectionPool, Redis

from app.core.config import settings


class RedisClient:
    _redis_client: Optional[Redis] = None
    _pool: Optional[ConnectionPool] = None

    @classmethod
    async def init_redis_pool(cls) -> None:
        """
        初始化Redis连接池
        """
        try:
            cls._pool = ConnectionPool(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                db=settings.REDIS_DB,
                password=settings.REDIS_PASSWORD,
                max_connections=settings.REDIS_POOL_SIZE,
                socket_timeout=settings.REDIS_POOL_TIMEOUT,
                decode_responses=True,  # 自动将响应解码为字符串
            )
            cls._redis_client = Redis(connection_pool=cls._pool)
            await cls._redis_client.ping()  # 测试连接

            # 在多进程环境下，只在主进程或第一次初始化时打印详细信息
            import os
            worker_id = os.environ.get('WORKER_PID', os.getpid())
            is_gunicorn = os.environ.get('GUNICORN_WORKER') == 'true'

            if is_gunicorn:
                logger.info(f"Worker {worker_id}: Redis连接池初始化成功")
            else:
                logger.info(f"Redis连接池初始化成功，Host: {settings.REDIS_HOST}, Port: {settings.REDIS_PORT}")

        except Exception as e:
            logger.error(f"Redis连接池初始化失败: {e}")
            raise e

    @classmethod
    async def close_redis_pool(cls) -> None:
        """
        关闭Redis连接池 - 增强版清理
        """
        try:
            import os
            worker_id = os.environ.get('WORKER_PID', os.getpid())
            is_gunicorn = os.environ.get('GUNICORN_WORKER') == 'true'

            if is_gunicorn:
                logger.debug(f"Worker {worker_id}: 开始关闭Redis连接池...")
            else:
                logger.info("开始关闭Redis连接池...")

            if cls._redis_client:
                # 等待所有待处理的操作完成
                try:
                    await cls._redis_client.close()
                    if not is_gunicorn:
                        logger.info("Redis客户端已关闭")
                except Exception as e:
                    logger.warning(f"关闭Redis客户端时出错: {e}")

            if cls._pool:
                # 断开连接池
                try:
                    await cls._pool.disconnect()
                    if not is_gunicorn:
                        logger.info("Redis连接池已断开")
                except Exception as e:
                    logger.warning(f"断开Redis连接池时出错: {e}")

            # 清空引用
            cls._redis_client = None
            cls._pool = None

            if is_gunicorn:
                logger.debug(f"Worker {worker_id}: Redis连接池清理完成")
            else:
                logger.info("Redis连接池清理完成")

        except Exception as e:
            logger.error(f"Redis连接池清理过程中出错: {e}")
            # 不再抛出异常，避免影响应用关闭

    @classmethod
    def get_redis_client(cls) -> Redis:
        """
        获取Redis客户端实例
        """
        assert cls._redis_client is not None, "Redis client is not initialized"
        return cls._redis_client


@asynccontextmanager
async def redis_lifespan(app: FastAPI):
    """
    Redis连接的生命周期管理
    """
    await RedisClient.init_redis_pool()
    yield
    await RedisClient.close_redis_pool()
