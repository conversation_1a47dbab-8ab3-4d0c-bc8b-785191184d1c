import traceback

from fastapi import Request
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from loguru import logger
from pydantic import ValidationError
from starlette.exceptions import HTTPException
from starlette.status import HTTP_404_NOT_FOUND

from app.core.exceptions import APIException
from app.utils.http_return_message import MSG


def handle_api_exception(request: Request, exc: APIException) -> JSONResponse:
    error_response = {
        "code": exc.code,
        "msg": exc.msg,
        "data": None,
    }
    logger.error(f"HTTP Error {exc.code} at {request.url}: {exc.msg}")
    return JSONResponse(content=error_response)


def handle_http_exception(request: Request, exc: HTTPException) -> JSONResponse:
    error_response = {
        "code": exc.status_code,
        "msg": exc.detail,
        "data": None,
    }
    logger.error(f"HTTP Error {exc.status_code} at {request.url}: {exc.detail}")
    return JSONResponse(content=error_response)


def handle_validation_error(request: Request, exc: RequestValidationError) -> JSONResponse:
    # 错误消息映射表
    errors = []
    for error in exc.errors():
        # error_location = ".".join(str(loc) for loc in error["loc"])
        error_location = error["loc"][-1]
        error_type = error["type"]
        error_msg = MSG.get(error["msg"], error["msg"])

        errors.append(f"{error_location}: {error_msg}")

    error_response = {
        "code": 400,
        "msg": "请求参数验证失败：{}".format("; ".join(errors)),
        "data": None,
    }
    logger.error(f"Validation error at {request.url}: {errors}")
    return JSONResponse(content=error_response)


def handle_pydantic_validation_error(request: Request, exc: ValidationError) -> JSONResponse:
    errors = []
    for error in exc.errors():
        error_location = " -> ".join(str(loc) for loc in error["loc"])
        errors.append(f"{error_location}: {error['msg']}")

    error_response = {
        "success": False,
        "code": 400,
        "msg": "数据验证失败",
        "data": {"details": errors},
    }
    return JSONResponse(content=error_response)


def handle_unexpected_error(request: Request, exc: Exception) -> JSONResponse:
    error_response = {
        "success": False,
        "code": 500,
        "msg": "服务器内部错误",
        "data": None,
    }

    # 使用 loguru 记录详细的错误信息，包括堆栈跟踪
    # logger.error(f"Unexpected error at {request.url}: {str(exc)} .{traceback.format_exc()}")
    return JSONResponse(content=error_response)


def handle_404_error(request: Request, exc: Exception) -> JSONResponse:
    """
    处理路由不存在（404）错误
    """
    error_response = {
        "code": HTTP_404_NOT_FOUND,
        "msg": f"路由 {request.url} 不存在",
        "data": None,
    }
    logger.warning(f"Route not found at {request.url}")
    return JSONResponse(content=error_response, status_code=HTTP_404_NOT_FOUND)
