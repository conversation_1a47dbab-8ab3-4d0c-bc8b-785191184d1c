# -*- coding: utf-8 -*-
from typing import Any, ClassVar, Dict, Optional, TypeVar

from fastapi import Query
from fastapi_pagination import Params
from pydantic import BaseModel, ConfigDict, field_validator
from pydantic._internal._model_construction import ModelMetaclass
from pydantic.fields import FieldInfo
from tortoise.queryset import QuerySet

T = TypeVar("T")  # 模型类型


class FilterFieldInfo(FieldInfo):
    """自定义字段信息类，用于存储过滤条件"""

    def __init__(self, *, lookup_expr: str = "exact", field_name: str = None, method: str = None, **kwargs):
        super().__init__(**kwargs)
        self.lookup_expr = lookup_expr
        self.field_name = field_name
        self.method = method


def FilterField(default: Any = None, *, lookup_expr: str = "exact", field_name: str = None, method: str = None, **kwargs):
    """创建一个过滤字段"""
    field_info = FilterFieldInfo(
        default=default,
        lookup_expr=lookup_expr,
        field_name=field_name,
        method=method,
        **{k: v for k, v in kwargs.items() if k != "alias"},
    )
    return field_info


class FilterSetMetaclass(ModelMetaclass):
    """元类，用于收集过滤器字段"""

    def __new__(mcs, name, bases, namespace, **kwargs):
        # 收集所有 FilterFieldInfo 字段
        filters = {}
        for name, field in namespace.items():
            if isinstance(field, FilterFieldInfo):
                filters[name] = field

        # 创建类
        cls = super().__new__(mcs, name, bases, namespace, **kwargs)
        cls.__filters__ = {**getattr(cls, "__filters__", {}), **filters}
        return cls


class BaseFilterSet(BaseModel, metaclass=FilterSetMetaclass):
    """过滤器集合基类"""

    __filters__: ClassVar[Dict[str, FilterFieldInfo]] = {}

    # Pydantic v2 配置
    model_config = ConfigDict(arbitrary_types_allowed=True)

    @classmethod
    def get_filters(cls) -> Dict[str, FilterFieldInfo]:
        """获取所有过滤器字段"""
        return cls.__filters__

    async def filter_queryset(self, queryset: QuerySet) -> QuerySet:
        """过滤查询集"""

        for field_name, field_info in self.get_filters().items():
            value = getattr(self, field_name, None)
            if value is None:
                continue

            if not value:
                continue

            if field_info.method:
                method = getattr(self, field_info.method, None)

                if method:
                    queryset = await method(queryset, field_name, value)
            else:
                if field_info.lookup_expr == "exact":
                    queryset = queryset.filter(**{field_info.field_name or field_name: value})
                else:
                    queryset = queryset.filter(**{f"{field_info.field_name or field_name}__{field_info.lookup_expr}": value})

        return queryset


class PageSizePaginatorParams(Params):
    """分页参数"""

    page: Optional[int | None] = Query(1, description="页码")
    size: Optional[int | None] = Query(20, description="每页大小")

    @field_validator("size")
    def validate_size(cls, v):
        if not v:
            return 20

        return v

    @field_validator("page")
    def validate_page(cls, v):
        if not v:
            return 1

        return v


__all__ = [
    "PageSizePaginatorParams",
    "BaseFilterSet",
    "FilterField",
]
