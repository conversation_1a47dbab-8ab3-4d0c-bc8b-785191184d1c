# -*- coding: utf-8 -*-
from datetime import datetime
from typing import Optional

from pydantic import Field

from app.filters.base import BaseFilterSet, PageSizePaginatorParams


class OrderFilterQueryParams(BaseFilterSet, PageSizePaginatorParams):
    order_id: Optional[str | None] = Field(None, description="订单编号")
    cert_sn_code: Optional[str | None] = Field(None, description="证书编号")
    product_sn_code: Optional[str | None] = Field(None, description="商品序列号")
    erp_order_id: Optional[str | None] = Field(None, description="erp订单编号")
    author_id: Optional[int | None] = Field(None, description="达人ID")
    shop_id: Optional[int | None] = Field(None, description="店铺ID")

    time_type: Optional[str | None] = Field(None, description="时间类型")

    pay_time_start: Optional[datetime | None] = Field(None, description="支付时间开始")
    pay_time_end: Optional[datetime | None] = Field(None, description="支付时间结束")
    order_time_start: Optional[datetime | None] = Field(None, description="订单时间开始")
    order_time_end: Optional[datetime | None] = Field(None, description="订单时间结束")
    send_date_start: Optional[datetime | None] = Field(None, description="发货时间开始")
    send_date_end: Optional[datetime | None] = Field(None, description="发货时间结束")
