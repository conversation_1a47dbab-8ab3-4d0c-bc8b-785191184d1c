from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "aerich" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "version" VARCHAR(255) NOT NULL,
    "app" VARCHAR(100) NOT NULL,
    "content" JSONB NOT NULL
);
CREATE TABLE IF NOT EXISTS "cert_companies" (
    "id" BIGSERIAL NOT NULL PRIMARY KEY,
    "create_user_id" INT,
    "update_user_id" INT,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "create_time" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "update_time" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "name" VARCHAR(100) NOT NULL,
    "company_id" VARCHAR(100) NOT NULL UNIQUE,
    "province_id" VARCHAR(12),
    "city_id" VARCHAR(12),
    "district_id" VARCHAR(12),
    "address" VARCHAR(255),
    "contact_user" VARCHAR(32),
    "contact_phone" VARCHAR(20)
);
CREATE INDEX IF NOT EXISTS "idx_cert_compan_company_22b5d9" ON "cert_companies" ("company_id");
COMMENT ON COLUMN "cert_companies"."id" IS '自增主键';
COMMENT ON COLUMN "cert_companies"."create_user_id" IS '创建人ID';
COMMENT ON COLUMN "cert_companies"."update_user_id" IS '更新人ID';
COMMENT ON COLUMN "cert_companies"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "cert_companies"."create_time" IS '创建时间';
COMMENT ON COLUMN "cert_companies"."update_time" IS '更新时间';
COMMENT ON COLUMN "cert_companies"."name" IS '公司名称';
COMMENT ON COLUMN "cert_companies"."company_id" IS '公司ID';
COMMENT ON COLUMN "cert_companies"."province_id" IS '省份ID';
COMMENT ON COLUMN "cert_companies"."city_id" IS '城市ID';
COMMENT ON COLUMN "cert_companies"."district_id" IS '区县ID';
COMMENT ON COLUMN "cert_companies"."address" IS '详细地址';
COMMENT ON COLUMN "cert_companies"."contact_user" IS '联系人';
COMMENT ON COLUMN "cert_companies"."contact_phone" IS '联系电话';
COMMENT ON TABLE "cert_companies" IS '公司信息表';
CREATE TABLE IF NOT EXISTS "cert_users" (
    "id" BIGSERIAL NOT NULL PRIMARY KEY,
    "create_user_id" INT,
    "update_user_id" INT,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "create_time" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "update_time" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "user_id" BIGINT NOT NULL UNIQUE,
    "username" VARCHAR(50) NOT NULL UNIQUE,
    "real_name" VARCHAR(12),
    "nickname" VARCHAR(64),
    "avatar" VARCHAR(255),
    "password" VARCHAR(255) NOT NULL,
    "mobile" VARCHAR(20) NOT NULL UNIQUE,
    "status" SMALLINT NOT NULL DEFAULT 1,
    "is_superuser" BOOL NOT NULL DEFAULT False,
    "last_login_time" TIMESTAMPTZ,
    "roles" JSONB,
    "company_id" BIGINT
);
COMMENT ON COLUMN "cert_users"."id" IS '自增主键';
COMMENT ON COLUMN "cert_users"."create_user_id" IS '创建人ID';
COMMENT ON COLUMN "cert_users"."update_user_id" IS '更新人ID';
COMMENT ON COLUMN "cert_users"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "cert_users"."create_time" IS '创建时间';
COMMENT ON COLUMN "cert_users"."update_time" IS '更新时间';
COMMENT ON COLUMN "cert_users"."user_id" IS '用户ID';
COMMENT ON COLUMN "cert_users"."username" IS '用户名';
COMMENT ON COLUMN "cert_users"."real_name" IS '真实姓名';
COMMENT ON COLUMN "cert_users"."nickname" IS '昵称';
COMMENT ON COLUMN "cert_users"."avatar" IS '头像';
COMMENT ON COLUMN "cert_users"."password" IS '密码';
COMMENT ON COLUMN "cert_users"."mobile" IS '手机号';
COMMENT ON COLUMN "cert_users"."status" IS '激活状态(1:正常, 2:禁用)';
COMMENT ON COLUMN "cert_users"."is_superuser" IS '是否为超级管理员';
COMMENT ON COLUMN "cert_users"."last_login_time" IS '最后登录时间';
COMMENT ON COLUMN "cert_users"."roles" IS '角色列表';
COMMENT ON COLUMN "cert_users"."company_id" IS '公司ID';
COMMENT ON TABLE "cert_users" IS '用户信息表';
CREATE TABLE IF NOT EXISTS "cert_sequence" (
    "name" VARCHAR(100) NOT NULL PRIMARY KEY,
    "last" BIGINT NOT NULL DEFAULT 0
);
COMMENT ON COLUMN "cert_sequence"."name" IS '序号名称';
COMMENT ON COLUMN "cert_sequence"."last" IS '当前序号';
COMMENT ON TABLE "cert_sequence" IS '序号生成器';
CREATE TABLE IF NOT EXISTS "cert_shipping_subscribe" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "msg_from" VARCHAR(100),
    "logistics_company_id" VARCHAR(100),
    "logistics_company" VARCHAR(100),
    "logistics_order_id" VARCHAR(100) NOT NULL,
    "order_id" VARCHAR(100) NOT NULL,
    "send_date" TIMESTAMPTZ,
    "total_qty" INT NOT NULL DEFAULT 0,
    "items" JSONB,
    "create_date" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX IF NOT EXISTS "idx_cert_shippi_logisti_b18ae1" ON "cert_shipping_subscribe" ("logistics_order_id");
CREATE INDEX IF NOT EXISTS "idx_cert_shippi_order_i_8a1021" ON "cert_shipping_subscribe" ("order_id");
COMMENT ON COLUMN "cert_shipping_subscribe"."msg_from" IS '消息来源:JST(聚水潭)/DD(抖店)';
COMMENT ON COLUMN "cert_shipping_subscribe"."logistics_company_id" IS '物流公司编码';
COMMENT ON COLUMN "cert_shipping_subscribe"."logistics_company" IS '物流公司';
COMMENT ON COLUMN "cert_shipping_subscribe"."logistics_order_id" IS '物流单号';
COMMENT ON COLUMN "cert_shipping_subscribe"."order_id" IS '线上单号';
COMMENT ON COLUMN "cert_shipping_subscribe"."send_date" IS '发货时间';
COMMENT ON COLUMN "cert_shipping_subscribe"."total_qty" IS '总数量';
COMMENT ON COLUMN "cert_shipping_subscribe"."items" IS '商品信息';
COMMENT ON COLUMN "cert_shipping_subscribe"."create_date" IS '创建时间';
COMMENT ON TABLE "cert_shipping_subscribe" IS '发货订阅表';
CREATE TABLE IF NOT EXISTS "cert_inbound_order" (
    "id" BIGSERIAL NOT NULL PRIMARY KEY,
    "create_user_id" INT,
    "update_user_id" INT,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "create_time" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "update_time" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "code" VARCHAR(32) NOT NULL,
    "check_company_name" VARCHAR(64) NOT NULL,
    "product_count" INT NOT NULL DEFAULT 1,
    "cert_count" INT NOT NULL DEFAULT 1,
    "waybill_count" INT NOT NULL DEFAULT 1,
    "status" INT NOT NULL DEFAULT 1,
    "check_user_id" BIGINT NOT NULL REFERENCES "cert_users" ("id") ON DELETE CASCADE,
    "company_id" BIGINT NOT NULL,
    CONSTRAINT "uid_cert_inboun_code_30fc48" UNIQUE ("code", "company_id")
);
CREATE INDEX IF NOT EXISTS "idx_cert_inboun_code_3f1ff0" ON "cert_inbound_order" ("code");
COMMENT ON COLUMN "cert_inbound_order"."id" IS '自增主键';
COMMENT ON COLUMN "cert_inbound_order"."create_user_id" IS '创建人ID';
COMMENT ON COLUMN "cert_inbound_order"."update_user_id" IS '更新人ID';
COMMENT ON COLUMN "cert_inbound_order"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "cert_inbound_order"."create_time" IS '创建时间';
COMMENT ON COLUMN "cert_inbound_order"."update_time" IS '更新时间';
COMMENT ON COLUMN "cert_inbound_order"."code" IS '入库单号';
COMMENT ON COLUMN "cert_inbound_order"."check_company_name" IS '公司名称';
COMMENT ON COLUMN "cert_inbound_order"."product_count" IS '商品数量';
COMMENT ON COLUMN "cert_inbound_order"."cert_count" IS '证书数量';
COMMENT ON COLUMN "cert_inbound_order"."waybill_count" IS '面单数量';
COMMENT ON COLUMN "cert_inbound_order"."status" IS '状态(1:待扫描, 2:待发货, 3:发货中, 4:已完成)';
COMMENT ON COLUMN "cert_inbound_order"."check_user_id" IS '清点人员';
COMMENT ON COLUMN "cert_inbound_order"."company_id" IS '公司';
COMMENT ON TABLE "cert_inbound_order" IS '入库单';
CREATE TABLE IF NOT EXISTS "cert_inbound_order_detail" (
    "id" BIGSERIAL NOT NULL PRIMARY KEY,
    "create_user_id" INT,
    "update_user_id" INT,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "create_time" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "update_time" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "code" VARCHAR(32) NOT NULL,
    "shipping_type" INT NOT NULL DEFAULT 1,
    "cert_type" INT NOT NULL DEFAULT 1,
    "batch_count" INT NOT NULL DEFAULT 1,
    "product_count" INT NOT NULL DEFAULT 1,
    "cert_count" INT NOT NULL DEFAULT 1,
    "company_id" BIGINT NOT NULL,
    "inbound_order_id" BIGINT NOT NULL,
    "shipping_user_id" BIGINT,
    CONSTRAINT "uid_cert_inboun_code_a61472" UNIQUE ("code", "company_id")
);
CREATE INDEX IF NOT EXISTS "idx_cert_inboun_code_9bf3cb" ON "cert_inbound_order_detail" ("code");
COMMENT ON COLUMN "cert_inbound_order_detail"."id" IS '自增主键';
COMMENT ON COLUMN "cert_inbound_order_detail"."create_user_id" IS '创建人ID';
COMMENT ON COLUMN "cert_inbound_order_detail"."update_user_id" IS '更新人ID';
COMMENT ON COLUMN "cert_inbound_order_detail"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "cert_inbound_order_detail"."create_time" IS '创建时间';
COMMENT ON COLUMN "cert_inbound_order_detail"."update_time" IS '更新时间';
COMMENT ON COLUMN "cert_inbound_order_detail"."code" IS '任务单号';
COMMENT ON COLUMN "cert_inbound_order_detail"."shipping_type" IS '发货类型(1:一单一件, 2:一单两件, 3:一单三件, 4:一单多件)';
COMMENT ON COLUMN "cert_inbound_order_detail"."cert_type" IS '发证方式(1:一物一证, 2:随机发证, 3:不发证书)';
COMMENT ON COLUMN "cert_inbound_order_detail"."batch_count" IS '每批数量';
COMMENT ON COLUMN "cert_inbound_order_detail"."product_count" IS '商品数量';
COMMENT ON COLUMN "cert_inbound_order_detail"."cert_count" IS '证书数量';
COMMENT ON COLUMN "cert_inbound_order_detail"."company_id" IS '公司';
COMMENT ON COLUMN "cert_inbound_order_detail"."inbound_order_id" IS '入库单';
COMMENT ON COLUMN "cert_inbound_order_detail"."shipping_user_id" IS '发货人';
COMMENT ON TABLE "cert_inbound_order_detail" IS '入库单明细';
CREATE TABLE IF NOT EXISTS "cert_shipping_waybill" (
    "id" BIGSERIAL NOT NULL PRIMARY KEY,
    "create_user_id" INT,
    "update_user_id" INT,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "create_time" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "update_time" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "waybill_code" VARCHAR(32) NOT NULL UNIQUE,
    "order_id" VARCHAR(128),
    "logistics_company" VARCHAR(100),
    "send_date" TIMESTAMPTZ,
    "total_qty" INT NOT NULL DEFAULT 0,
    "inbound_order_code" VARCHAR(32),
    "inbound_order_detail_code" VARCHAR(32),
    "shipping_user_name" VARCHAR(64),
    "company_id" BIGINT NOT NULL,
    "inbound_order_id" BIGINT NOT NULL,
    "inbound_order_detail_id" BIGINT NOT NULL,
    "shipping_user_id" BIGINT
);
CREATE INDEX IF NOT EXISTS "idx_cert_shippi_waybill_8228df" ON "cert_shipping_waybill" ("waybill_code");
CREATE INDEX IF NOT EXISTS "idx_cert_shippi_order_i_20b68a" ON "cert_shipping_waybill" ("order_id");
CREATE INDEX IF NOT EXISTS "idx_cert_shippi_inbound_208a7e" ON "cert_shipping_waybill" ("inbound_order_code");
CREATE INDEX IF NOT EXISTS "idx_cert_shippi_inbound_a7e6cc" ON "cert_shipping_waybill" ("inbound_order_detail_code");
CREATE INDEX IF NOT EXISTS "idx_cert_shippi_waybill_3c45a5" ON "cert_shipping_waybill" ("waybill_code", "company_id");
CREATE INDEX IF NOT EXISTS "idx_cert_shippi_order_i_a2d8bc" ON "cert_shipping_waybill" ("order_id", "company_id");
CREATE INDEX IF NOT EXISTS "idx_cert_shippi_shippin_06bb1e" ON "cert_shipping_waybill" ("shipping_user_id", "company_id");
CREATE INDEX IF NOT EXISTS "idx_cert_shippi_inbound_e193e1" ON "cert_shipping_waybill" ("inbound_order_code", "company_id");
CREATE INDEX IF NOT EXISTS "idx_cert_shippi_inbound_00fcbe" ON "cert_shipping_waybill" ("inbound_order_detail_code", "company_id");
COMMENT ON COLUMN "cert_shipping_waybill"."id" IS '自增主键';
COMMENT ON COLUMN "cert_shipping_waybill"."create_user_id" IS '创建人ID';
COMMENT ON COLUMN "cert_shipping_waybill"."update_user_id" IS '更新人ID';
COMMENT ON COLUMN "cert_shipping_waybill"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "cert_shipping_waybill"."create_time" IS '创建时间';
COMMENT ON COLUMN "cert_shipping_waybill"."update_time" IS '更新时间';
COMMENT ON COLUMN "cert_shipping_waybill"."waybill_code" IS '快递单号/面单编号';
COMMENT ON COLUMN "cert_shipping_waybill"."order_id" IS '线上订单号';
COMMENT ON COLUMN "cert_shipping_waybill"."logistics_company" IS '物流公司';
COMMENT ON COLUMN "cert_shipping_waybill"."send_date" IS '发货时间';
COMMENT ON COLUMN "cert_shipping_waybill"."total_qty" IS '总数量';
COMMENT ON COLUMN "cert_shipping_waybill"."inbound_order_code" IS '入库单号(冗余)';
COMMENT ON COLUMN "cert_shipping_waybill"."inbound_order_detail_code" IS '入库单明细号(冗余)';
COMMENT ON COLUMN "cert_shipping_waybill"."shipping_user_name" IS '发货人姓名(冗余)';
COMMENT ON COLUMN "cert_shipping_waybill"."company_id" IS '公司';
COMMENT ON COLUMN "cert_shipping_waybill"."inbound_order_id" IS '入库单';
COMMENT ON COLUMN "cert_shipping_waybill"."inbound_order_detail_id" IS '入库单明细';
COMMENT ON COLUMN "cert_shipping_waybill"."shipping_user_id" IS '发货人';
COMMENT ON TABLE "cert_shipping_waybill" IS '面单表';
CREATE TABLE IF NOT EXISTS "cert_waybill_product" (
    "id" BIGSERIAL NOT NULL PRIMARY KEY,
    "create_user_id" INT,
    "update_user_id" INT,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "create_time" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "update_time" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "item_type" INT NOT NULL DEFAULT 1,
    "sku_id" VARCHAR(128),
    "product_name" VARCHAR(256),
    "product_image" VARCHAR(500),
    "quantity" INT NOT NULL DEFAULT 0,
    "outer_oi_id" VARCHAR(128) NOT NULL,
    "oi_id" VARCHAR(128),
    "cert_sn_code" VARCHAR(128) UNIQUE,
    "cert_link" VARCHAR(500),
    "cert_type" INT,
    "product_sn_code" VARCHAR(128),
    "bind_status" INT NOT NULL DEFAULT 1,
    "bind_time" TIMESTAMPTZ,
    "waybill_code" VARCHAR(32),
    "order_id" VARCHAR(128) NOT NULL,
    "send_date" TIMESTAMPTZ,
    "company_id" BIGINT NOT NULL,
    "inbound_order_id" BIGINT NOT NULL,
    "inbound_order_detail_id" BIGINT NOT NULL,
    "waybill_id" BIGINT NOT NULL
);
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_item_ty_92699b" ON "cert_waybill_product" ("item_type");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_sku_id_2e6ca1" ON "cert_waybill_product" ("sku_id");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_outer_o_a8ab24" ON "cert_waybill_product" ("outer_oi_id");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_oi_id_64236e" ON "cert_waybill_product" ("oi_id");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_cert_sn_0a0e00" ON "cert_waybill_product" ("cert_sn_code");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_cert_ty_4daff1" ON "cert_waybill_product" ("cert_type");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_product_86b7a0" ON "cert_waybill_product" ("product_sn_code");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_bind_st_4e83bd" ON "cert_waybill_product" ("bind_status");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_waybill_34b692" ON "cert_waybill_product" ("waybill_code");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_order_i_8139f6" ON "cert_waybill_product" ("order_id");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_item_ty_d05867" ON "cert_waybill_product" ("item_type", "company_id");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_sku_id_599cad" ON "cert_waybill_product" ("sku_id", "company_id");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_cert_sn_dec909" ON "cert_waybill_product" ("cert_sn_code", "company_id");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_product_8e1718" ON "cert_waybill_product" ("product_sn_code", "company_id");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_outer_o_be9e9f" ON "cert_waybill_product" ("outer_oi_id", "company_id");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_waybill_f0cb26" ON "cert_waybill_product" ("waybill_code", "company_id");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_order_i_3e137a" ON "cert_waybill_product" ("order_id", "company_id");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_cert_ty_ce2e82" ON "cert_waybill_product" ("cert_type", "company_id");
CREATE INDEX IF NOT EXISTS "idx_cert_waybil_waybill_cfb411" ON "cert_waybill_product" ("waybill_id", "item_type");
COMMENT ON COLUMN "cert_waybill_product"."id" IS '自增主键';
COMMENT ON COLUMN "cert_waybill_product"."create_user_id" IS '创建人ID';
COMMENT ON COLUMN "cert_waybill_product"."update_user_id" IS '更新人ID';
COMMENT ON COLUMN "cert_waybill_product"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "cert_waybill_product"."create_time" IS '创建时间';
COMMENT ON COLUMN "cert_waybill_product"."update_time" IS '更新时间';
COMMENT ON COLUMN "cert_waybill_product"."item_type" IS '记录类型(1:仅商品, 2:商品+证书, 3:随机发证)';
COMMENT ON COLUMN "cert_waybill_product"."sku_id" IS '商品SKU_ID';
COMMENT ON COLUMN "cert_waybill_product"."product_name" IS '商品名称';
COMMENT ON COLUMN "cert_waybill_product"."product_image" IS '商品图片';
COMMENT ON COLUMN "cert_waybill_product"."quantity" IS '商品数量';
COMMENT ON COLUMN "cert_waybill_product"."outer_oi_id" IS '外部订单项ID';
COMMENT ON COLUMN "cert_waybill_product"."oi_id" IS '订单项ID';
COMMENT ON COLUMN "cert_waybill_product"."cert_sn_code" IS '证书SN码';
COMMENT ON COLUMN "cert_waybill_product"."cert_link" IS '证书链接';
COMMENT ON COLUMN "cert_waybill_product"."cert_type" IS '证书类型(1:一物一证, 2:随机发证)';
COMMENT ON COLUMN "cert_waybill_product"."product_sn_code" IS '商品SN码';
COMMENT ON COLUMN "cert_waybill_product"."bind_status" IS '绑定状态(1:未绑定, 2:已绑定)';
COMMENT ON COLUMN "cert_waybill_product"."bind_time" IS '绑定时间';
COMMENT ON COLUMN "cert_waybill_product"."waybill_code" IS '快递单号(冗余)';
COMMENT ON COLUMN "cert_waybill_product"."order_id" IS '线上订单号(冗余)';
COMMENT ON COLUMN "cert_waybill_product"."send_date" IS '发货时间(冗余)';
COMMENT ON COLUMN "cert_waybill_product"."company_id" IS '公司';
COMMENT ON COLUMN "cert_waybill_product"."inbound_order_id" IS '入库单';
COMMENT ON COLUMN "cert_waybill_product"."inbound_order_detail_id" IS '入库单明细';
COMMENT ON COLUMN "cert_waybill_product"."waybill_id" IS '面单';
COMMENT ON TABLE "cert_waybill_product" IS '面单商品表（合并证书绑定）';
CREATE TABLE IF NOT EXISTS "orders_order" (
    "id" BIGSERIAL NOT NULL PRIMARY KEY,
    "order_id" VARCHAR(64) NOT NULL,
    "ex_order_id" VARCHAR(64) NOT NULL,
    "order_status" VARCHAR(6) NOT NULL,
    "order_status_desc" VARCHAR(16),
    "main_status" SMALLINT NOT NULL,
    "main_status_desc" VARCHAR(64),
    "order_type" INT NOT NULL,
    "order_platform" VARCHAR(6) NOT NULL,
    "data_source" VARCHAR(3) NOT NULL,
    "biz" SMALLINT NOT NULL,
    "order_time" TIMESTAMPTZ NOT NULL,
    "pay_time" TIMESTAMPTZ,
    "finish_time" TIMESTAMPTZ,
    "order_expire_time" TIMESTAMPTZ,
    "appointment_ship_time" TIMESTAMPTZ,
    "pay_type" INT NOT NULL,
    "channel_payment_no" VARCHAR(255),
    "b_type" INT NOT NULL,
    "trade_type" INT,
    "order_amount" DECIMAL(10,2),
    "pay_amount" DECIMAL(10,2),
    "paid_amount" DECIMAL(10,2),
    "cost_price_amount" DECIMAL(10,2),
    "post_amount" DECIMAL(10,2),
    "post_origin_amount" DECIMAL(10,2),
    "tax_amount" DECIMAL(10,2),
    "shop_id" VARCHAR(64),
    "distributor_id" INT NOT NULL,
    "cancel_reason" VARCHAR(255),
    "create_user" VARCHAR(32),
    "update_user" VARCHAR(32),
    "create_date" TIMESTAMPTZ,
    "update_date" TIMESTAMPTZ,
    "parent_id" VARCHAR(64),
    "product_match_state" SMALLINT NOT NULL,
    "delivery_method" VARCHAR(12)
);
COMMENT ON COLUMN "orders_order"."id" IS 'id';
COMMENT ON COLUMN "orders_order"."order_id" IS 'order_id';
COMMENT ON COLUMN "orders_order"."ex_order_id" IS 'ex_order_id';
COMMENT ON COLUMN "orders_order"."order_status" IS 'order_status';
COMMENT ON COLUMN "orders_order"."order_status_desc" IS 'order_status_desc';
COMMENT ON COLUMN "orders_order"."main_status" IS 'main_status';
COMMENT ON COLUMN "orders_order"."main_status_desc" IS 'main_status_desc';
COMMENT ON COLUMN "orders_order"."order_type" IS 'order_type';
COMMENT ON COLUMN "orders_order"."order_platform" IS 'order_platform';
COMMENT ON COLUMN "orders_order"."data_source" IS 'data_source';
COMMENT ON COLUMN "orders_order"."biz" IS 'biz';
COMMENT ON COLUMN "orders_order"."order_time" IS 'order_time';
COMMENT ON COLUMN "orders_order"."pay_time" IS 'pay_time';
COMMENT ON COLUMN "orders_order"."finish_time" IS 'finish_time';
COMMENT ON COLUMN "orders_order"."order_expire_time" IS 'order_expire_time';
COMMENT ON COLUMN "orders_order"."appointment_ship_time" IS 'appointment_ship_time';
COMMENT ON COLUMN "orders_order"."pay_type" IS 'pay_type';
COMMENT ON COLUMN "orders_order"."channel_payment_no" IS 'channel_payment_no';
COMMENT ON COLUMN "orders_order"."b_type" IS 'b_type';
COMMENT ON COLUMN "orders_order"."trade_type" IS 'trade_type';
COMMENT ON COLUMN "orders_order"."order_amount" IS 'order_amount';
COMMENT ON COLUMN "orders_order"."pay_amount" IS 'pay_amount';
COMMENT ON COLUMN "orders_order"."paid_amount" IS 'paid_amount';
COMMENT ON COLUMN "orders_order"."cost_price_amount" IS 'cost_price_amount';
COMMENT ON COLUMN "orders_order"."post_amount" IS 'post_amount';
COMMENT ON COLUMN "orders_order"."post_origin_amount" IS 'post_origin_amount';
COMMENT ON COLUMN "orders_order"."tax_amount" IS 'tax_amount';
COMMENT ON COLUMN "orders_order"."shop_id" IS 'shop_id';
COMMENT ON COLUMN "orders_order"."distributor_id" IS 'distributor_id';
COMMENT ON COLUMN "orders_order"."cancel_reason" IS 'cancel_reason';
COMMENT ON COLUMN "orders_order"."create_user" IS 'create_user';
COMMENT ON COLUMN "orders_order"."update_user" IS 'update_user';
COMMENT ON COLUMN "orders_order"."create_date" IS 'create_date';
COMMENT ON COLUMN "orders_order"."update_date" IS 'update_date';
COMMENT ON COLUMN "orders_order"."parent_id" IS 'parent_id';
COMMENT ON COLUMN "orders_order"."product_match_state" IS 'product_match_state';
COMMENT ON COLUMN "orders_order"."delivery_method" IS 'delivery_method';
COMMENT ON TABLE "orders_order" IS 'OrdersOrder 表';
CREATE TABLE IF NOT EXISTS "orders_orderitems" (
    "id" BIGSERIAL NOT NULL PRIMARY KEY,
    "order_id" VARCHAR(64) NOT NULL,
    "parent_ex_order_id" VARCHAR(100),
    "ex_sub_order_id" VARCHAR(64) NOT NULL,
    "raw_spec_code" VARCHAR(255),
    "spec_code" VARCHAR(255),
    "letters" VARCHAR(12),
    "code" VARCHAR(20),
    "distributor_id" INT NOT NULL,
    "company_id" INT NOT NULL,
    "sku_id" INT,
    "sku_version_id" BIGINT,
    "product_id" INT,
    "product_version_id" BIGINT,
    "sub_product_id" INT,
    "sub_product_version_id" BIGINT,
    "author_id" VARCHAR(100),
    "author_name" VARCHAR(255),
    "goods_price" DECIMAL(10,2),
    "goods_type" SMALLINT,
    "item_num" INT,
    "product_count" INT,
    "order_amount" DECIMAL(10,2),
    "pay_amount" DECIMAL(10,2),
    "paid_amount" DECIMAL(10,2),
    "cost_price_amount" DECIMAL(10,2),
    "post_amount" DECIMAL(10,2),
    "exp_ship_time" TIMESTAMPTZ,
    "finish_time" TIMESTAMPTZ,
    "ship_time" TIMESTAMPTZ,
    "confirm_receipt_time" TIMESTAMPTZ,
    "order_status" VARCHAR(12) NOT NULL,
    "order_status_desc" VARCHAR(16),
    "raw_order_status" VARCHAR(100),
    "from_mall" BOOL,
    "receiver_info" JSONB,
    "encrypt_post_receiver" TEXT,
    "encrypt_post_tel" TEXT,
    "ad_env_type" VARCHAR(32),
    "appointment_ship_time" TIMESTAMPTZ,
    "c_biz" INT,
    "sub_b_type" INT,
    "cancel_reason" VARCHAR(255),
    "channel_payment_no" VARCHAR(255),
    "other_info" JSONB,
    "category_id_info" JSONB,
    "has_tax" BOOL NOT NULL,
    "is_comment" SMALLINT,
    "logistics_receipt_time" TIMESTAMPTZ,
    "main_status" VARCHAR(32),
    "main_status_desc" VARCHAR(255),
    "mask_post_receiver" VARCHAR(255),
    "mask_post_tel" VARCHAR(255),
    "modify_amount" DECIMAL(10,2),
    "modify_post_amount" DECIMAL(10,2),
    "only_platform_cost_amount" DECIMAL(10,2),
    "order_expire_time" TIMESTAMPTZ,
    "order_level" INT NOT NULL,
    "origin_amount" DECIMAL(10,2),
    "packing_charge_amount" DECIMAL(10,2),
    "platform_cost_amount" DECIMAL(10,2),
    "post_insurance_amount" DECIMAL(10,2),
    "pre_sale_type" SMALLINT,
    "promotion_amount" DECIMAL(10,2),
    "promotion_pay_amount" DECIMAL(10,2),
    "promotion_platform_amount" DECIMAL(10,2),
    "promotion_redpack_amount" DECIMAL(10,2),
    "promotion_redpack_platform_amount" DECIMAL(10,2),
    "promotion_redpack_talent_amount" DECIMAL(10,2),
    "promotion_shop_amount" DECIMAL(10,2),
    "promotion_talent_amount" DECIMAL(10,2),
    "quality_inspection_status" INT NOT NULL,
    "receive_type" INT,
    "reduce_stock_type" SMALLINT NOT NULL,
    "shop_cost_amount" DECIMAL(10,2),
    "sku_order_tag_ui" JSONB,
    "spec" JSONB,
    "sum_amount" DECIMAL(10,2),
    "supplier_id" VARCHAR(255),
    "tax_amount" DECIMAL(10,2),
    "theme_type" VARCHAR(100),
    "trade_type" INT,
    "voucher_deduction_amount" DECIMAL(10,2),
    "given_product_activity_info" JSONB,
    "after_sale_status" INT,
    "after_sale_type" SMALLINT,
    "refund_status" SMALLINT,
    "inventory_list" JSONB,
    "logistics_info" JSONB,
    "raw_sku_id" VARCHAR(255),
    "raw_product_id" VARCHAR(255),
    "raw_product_name" VARCHAR(255),
    "raw_product_pic" VARCHAR(300),
    "raw_sku_specs" JSONB,
    "platform_update_time" TIMESTAMPTZ,
    "is_gift" BOOL NOT NULL,
    "create_user" VARCHAR(32),
    "update_user" VARCHAR(32),
    "create_date" TIMESTAMPTZ,
    "update_date" TIMESTAMPTZ,
    "relate_order_id" VARCHAR(64) NOT NULL,
    "order_time" TIMESTAMPTZ NOT NULL,
    "order_type" INT NOT NULL,
    "pay_time" TIMESTAMPTZ
);
COMMENT ON COLUMN "orders_orderitems"."id" IS 'id';
COMMENT ON COLUMN "orders_orderitems"."order_id" IS 'order_id';
COMMENT ON COLUMN "orders_orderitems"."parent_ex_order_id" IS 'parent_ex_order_id';
COMMENT ON COLUMN "orders_orderitems"."ex_sub_order_id" IS 'ex_sub_order_id';
COMMENT ON COLUMN "orders_orderitems"."raw_spec_code" IS 'raw_spec_code';
COMMENT ON COLUMN "orders_orderitems"."spec_code" IS 'spec_code';
COMMENT ON COLUMN "orders_orderitems"."letters" IS 'letters';
COMMENT ON COLUMN "orders_orderitems"."code" IS 'code';
COMMENT ON COLUMN "orders_orderitems"."distributor_id" IS 'distributor_id';
COMMENT ON COLUMN "orders_orderitems"."company_id" IS 'company_id';
COMMENT ON COLUMN "orders_orderitems"."sku_id" IS 'sku_id';
COMMENT ON COLUMN "orders_orderitems"."sku_version_id" IS 'sku_version_id';
COMMENT ON COLUMN "orders_orderitems"."product_id" IS 'product_id';
COMMENT ON COLUMN "orders_orderitems"."product_version_id" IS 'product_version_id';
COMMENT ON COLUMN "orders_orderitems"."sub_product_id" IS 'sub_product_id';
COMMENT ON COLUMN "orders_orderitems"."sub_product_version_id" IS 'sub_product_version_id';
COMMENT ON COLUMN "orders_orderitems"."author_id" IS 'author_id';
COMMENT ON COLUMN "orders_orderitems"."author_name" IS 'author_name';
COMMENT ON COLUMN "orders_orderitems"."goods_price" IS 'goods_price';
COMMENT ON COLUMN "orders_orderitems"."goods_type" IS 'goods_type';
COMMENT ON COLUMN "orders_orderitems"."item_num" IS 'item_num';
COMMENT ON COLUMN "orders_orderitems"."product_count" IS 'product_count';
COMMENT ON COLUMN "orders_orderitems"."order_amount" IS 'order_amount';
COMMENT ON COLUMN "orders_orderitems"."pay_amount" IS 'pay_amount';
COMMENT ON COLUMN "orders_orderitems"."paid_amount" IS 'paid_amount';
COMMENT ON COLUMN "orders_orderitems"."cost_price_amount" IS 'cost_price_amount';
COMMENT ON COLUMN "orders_orderitems"."post_amount" IS 'post_amount';
COMMENT ON COLUMN "orders_orderitems"."exp_ship_time" IS 'exp_ship_time';
COMMENT ON COLUMN "orders_orderitems"."finish_time" IS 'finish_time';
COMMENT ON COLUMN "orders_orderitems"."ship_time" IS 'ship_time';
COMMENT ON COLUMN "orders_orderitems"."confirm_receipt_time" IS 'confirm_receipt_time';
COMMENT ON COLUMN "orders_orderitems"."order_status" IS 'order_status';
COMMENT ON COLUMN "orders_orderitems"."order_status_desc" IS 'order_status_desc';
COMMENT ON COLUMN "orders_orderitems"."raw_order_status" IS 'raw_order_status';
COMMENT ON COLUMN "orders_orderitems"."from_mall" IS 'from_mall';
COMMENT ON COLUMN "orders_orderitems"."receiver_info" IS 'receiver_info';
COMMENT ON COLUMN "orders_orderitems"."encrypt_post_receiver" IS 'encrypt_post_receiver';
COMMENT ON COLUMN "orders_orderitems"."encrypt_post_tel" IS 'encrypt_post_tel';
COMMENT ON COLUMN "orders_orderitems"."ad_env_type" IS 'ad_env_type';
COMMENT ON COLUMN "orders_orderitems"."appointment_ship_time" IS 'appointment_ship_time';
COMMENT ON COLUMN "orders_orderitems"."c_biz" IS 'c_biz';
COMMENT ON COLUMN "orders_orderitems"."sub_b_type" IS 'sub_b_type';
COMMENT ON COLUMN "orders_orderitems"."cancel_reason" IS 'cancel_reason';
COMMENT ON COLUMN "orders_orderitems"."channel_payment_no" IS 'channel_payment_no';
COMMENT ON COLUMN "orders_orderitems"."other_info" IS 'other_info';
COMMENT ON COLUMN "orders_orderitems"."category_id_info" IS 'category_id_info';
COMMENT ON COLUMN "orders_orderitems"."has_tax" IS 'has_tax';
COMMENT ON COLUMN "orders_orderitems"."is_comment" IS 'is_comment';
COMMENT ON COLUMN "orders_orderitems"."logistics_receipt_time" IS 'logistics_receipt_time';
COMMENT ON COLUMN "orders_orderitems"."main_status" IS 'main_status';
COMMENT ON COLUMN "orders_orderitems"."main_status_desc" IS 'main_status_desc';
COMMENT ON COLUMN "orders_orderitems"."mask_post_receiver" IS 'mask_post_receiver';
COMMENT ON COLUMN "orders_orderitems"."mask_post_tel" IS 'mask_post_tel';
COMMENT ON COLUMN "orders_orderitems"."modify_amount" IS 'modify_amount';
COMMENT ON COLUMN "orders_orderitems"."modify_post_amount" IS 'modify_post_amount';
COMMENT ON COLUMN "orders_orderitems"."only_platform_cost_amount" IS 'only_platform_cost_amount';
COMMENT ON COLUMN "orders_orderitems"."order_expire_time" IS 'order_expire_time';
COMMENT ON COLUMN "orders_orderitems"."order_level" IS 'order_level';
COMMENT ON COLUMN "orders_orderitems"."origin_amount" IS 'origin_amount';
COMMENT ON COLUMN "orders_orderitems"."packing_charge_amount" IS 'packing_charge_amount';
COMMENT ON COLUMN "orders_orderitems"."platform_cost_amount" IS 'platform_cost_amount';
COMMENT ON COLUMN "orders_orderitems"."post_insurance_amount" IS 'post_insurance_amount';
COMMENT ON COLUMN "orders_orderitems"."pre_sale_type" IS 'pre_sale_type';
COMMENT ON COLUMN "orders_orderitems"."promotion_amount" IS 'promotion_amount';
COMMENT ON COLUMN "orders_orderitems"."promotion_pay_amount" IS 'promotion_pay_amount';
COMMENT ON COLUMN "orders_orderitems"."promotion_platform_amount" IS 'promotion_platform_amount';
COMMENT ON COLUMN "orders_orderitems"."promotion_redpack_amount" IS 'promotion_redpack_amount';
COMMENT ON COLUMN "orders_orderitems"."promotion_redpack_platform_amount" IS 'promotion_redpack_platform_amount';
COMMENT ON COLUMN "orders_orderitems"."promotion_redpack_talent_amount" IS 'promotion_redpack_talent_amount';
COMMENT ON COLUMN "orders_orderitems"."promotion_shop_amount" IS 'promotion_shop_amount';
COMMENT ON COLUMN "orders_orderitems"."promotion_talent_amount" IS 'promotion_talent_amount';
COMMENT ON COLUMN "orders_orderitems"."quality_inspection_status" IS 'quality_inspection_status';
COMMENT ON COLUMN "orders_orderitems"."receive_type" IS 'receive_type';
COMMENT ON COLUMN "orders_orderitems"."reduce_stock_type" IS 'reduce_stock_type';
COMMENT ON COLUMN "orders_orderitems"."shop_cost_amount" IS 'shop_cost_amount';
COMMENT ON COLUMN "orders_orderitems"."sku_order_tag_ui" IS 'sku_order_tag_ui';
COMMENT ON COLUMN "orders_orderitems"."spec" IS 'spec';
COMMENT ON COLUMN "orders_orderitems"."sum_amount" IS 'sum_amount';
COMMENT ON COLUMN "orders_orderitems"."supplier_id" IS 'supplier_id';
COMMENT ON COLUMN "orders_orderitems"."tax_amount" IS 'tax_amount';
COMMENT ON COLUMN "orders_orderitems"."theme_type" IS 'theme_type';
COMMENT ON COLUMN "orders_orderitems"."trade_type" IS 'trade_type';
COMMENT ON COLUMN "orders_orderitems"."voucher_deduction_amount" IS 'voucher_deduction_amount';
COMMENT ON COLUMN "orders_orderitems"."given_product_activity_info" IS 'given_product_activity_info';
COMMENT ON COLUMN "orders_orderitems"."after_sale_status" IS 'after_sale_status';
COMMENT ON COLUMN "orders_orderitems"."after_sale_type" IS 'after_sale_type';
COMMENT ON COLUMN "orders_orderitems"."refund_status" IS 'refund_status';
COMMENT ON COLUMN "orders_orderitems"."inventory_list" IS 'inventory_list';
COMMENT ON COLUMN "orders_orderitems"."logistics_info" IS 'logistics_info';
COMMENT ON COLUMN "orders_orderitems"."raw_sku_id" IS 'raw_sku_id';
COMMENT ON COLUMN "orders_orderitems"."raw_product_id" IS 'raw_product_id';
COMMENT ON COLUMN "orders_orderitems"."raw_product_name" IS 'raw_product_name';
COMMENT ON COLUMN "orders_orderitems"."raw_product_pic" IS 'raw_product_pic';
COMMENT ON COLUMN "orders_orderitems"."raw_sku_specs" IS 'raw_sku_specs';
COMMENT ON COLUMN "orders_orderitems"."platform_update_time" IS 'platform_update_time';
COMMENT ON COLUMN "orders_orderitems"."is_gift" IS 'is_gift';
COMMENT ON COLUMN "orders_orderitems"."create_user" IS 'create_user';
COMMENT ON COLUMN "orders_orderitems"."update_user" IS 'update_user';
COMMENT ON COLUMN "orders_orderitems"."create_date" IS 'create_date';
COMMENT ON COLUMN "orders_orderitems"."update_date" IS 'update_date';
COMMENT ON COLUMN "orders_orderitems"."relate_order_id" IS 'relate_order_id';
COMMENT ON COLUMN "orders_orderitems"."order_time" IS 'order_time';
COMMENT ON COLUMN "orders_orderitems"."order_type" IS 'order_type';
COMMENT ON COLUMN "orders_orderitems"."pay_time" IS 'pay_time';
COMMENT ON TABLE "orders_orderitems" IS 'OrdersOrderitems 表';
CREATE TABLE IF NOT EXISTS "cert_live_authors" (
    "id" BIGSERIAL NOT NULL PRIMARY KEY,
    "create_user_id" INT,
    "update_user_id" INT,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "create_time" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "update_time" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "author_id" VARCHAR(64) NOT NULL,
    "author_name" VARCHAR(128),
    "company_id" BIGINT NOT NULL
);
COMMENT ON COLUMN "cert_live_authors"."id" IS '自增主键';
COMMENT ON COLUMN "cert_live_authors"."create_user_id" IS '创建人ID';
COMMENT ON COLUMN "cert_live_authors"."update_user_id" IS '更新人ID';
COMMENT ON COLUMN "cert_live_authors"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "cert_live_authors"."create_time" IS '创建时间';
COMMENT ON COLUMN "cert_live_authors"."update_time" IS '更新时间';
COMMENT ON COLUMN "cert_live_authors"."author_id" IS '达人id';
COMMENT ON COLUMN "cert_live_authors"."author_name" IS '达人名称';
COMMENT ON COLUMN "cert_live_authors"."company_id" IS '公司';
COMMENT ON TABLE "cert_live_authors" IS '达人管理表';
CREATE TABLE IF NOT EXISTS "cert_shops" (
    "id" BIGSERIAL NOT NULL PRIMARY KEY,
    "create_user_id" INT,
    "update_user_id" INT,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "create_time" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "update_time" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "shop_id" VARCHAR(64) NOT NULL UNIQUE,
    "shop_name" VARCHAR(128),
    "company_id" BIGINT NOT NULL
);
COMMENT ON COLUMN "cert_shops"."id" IS '自增主键';
COMMENT ON COLUMN "cert_shops"."create_user_id" IS '创建人ID';
COMMENT ON COLUMN "cert_shops"."update_user_id" IS '更新人ID';
COMMENT ON COLUMN "cert_shops"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "cert_shops"."create_time" IS '创建时间';
COMMENT ON COLUMN "cert_shops"."update_time" IS '更新时间';
COMMENT ON COLUMN "cert_shops"."shop_id" IS '店铺id';
COMMENT ON COLUMN "cert_shops"."shop_name" IS '店铺名称';
COMMENT ON COLUMN "cert_shops"."company_id" IS '公司';
COMMENT ON TABLE "cert_shops" IS '店铺管理表';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        """
