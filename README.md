# ZhuLinks 证书绑定系统 - 项目分析文档

## 📋 项目概述

ZhuLinks 证书绑定系统是一个基于 FastAPI 构建的企业级证书管理和物流发货系统。该系统主要用于处理入库订单管理、发货面单管理、证书绑定等核心业务流程，支持多公司、多用户的
SaaS 架构。

### 核心业务流程

```
入库订单 → 任务分配 → 面单创建 → 商品添加 → 证书绑定 → 发货管理
```

## 🏗️ 技术架构

### 技术栈

- **后端框架**: FastAPI 0.115.12+ (异步 Web 框架)
- **数据库**: PostgreSQL (主数据库) + Redis (缓存)
- **ORM**: Tortoise-ORM 0.25.1+ (异步 ORM)
- **数据库迁移**: Aerich 0.9.0+
- **认证**: JWT (Python-jose)
- **密码加密**: Passlib + Argon2
- **日志**: Loguru
- **部署**: Gunicorn + Uvicorn
- **数据验证**: Pydantic
- **分页**: FastAPI-Pagination

### 系统架构层次

```
┌─────────────────┐
│   API 层        │  FastAPI 路由、中间件、依赖注入
├─────────────────┤
│   服务层        │  业务逻辑处理、事务管理
├─────────────────┤
│   模型层        │  Tortoise ORM 模型、数据验证
├─────────────────┤
│   数据层        │  PostgreSQL + Redis
└─────────────────┘
```

## 📁 项目结构

```
zhulinks_cert/
├── app/                          # 主应用目录
│   ├── api/                      # API 路由层
│   │   ├── endpoints/            # API 端点实现
│   │   │   ├── login_endpoints.py      # 登录认证
│   │   │   ├── user_endpoints.py       # 用户管理
│   │   │   ├── company_endpoints.py    # 公司管理
│   │   │   ├── inbound_order_endpoints.py    # 入库订单
│   │   │   ├── inbound_order_detail_endpoints.py  # 入库明细
│   │   │   ├── shipping_endpoints.py   # 发货管理
│   │   │   ├── orders_endpoints.py     # 订单查询
│   │   │   └── subscribe_endpoints.py  # 订阅管理
│   │   ├── dependencies.py       # 依赖注入 (JWT 认证)
│   │   └── v1_router.py          # API 路由注册
│   ├── core/                     # 核心配置
│   │   ├── config.py             # 应用配置
│   │   ├── exceptions.py         # 异常定义
│   │   ├── logging.py            # 日志配置
│   │   ├── redis.py              # Redis 连接
│   │   └── tortoise_config.py    # ORM 配置
│   ├── filters/                  # 查询过滤器
│   │   ├── base.py               # 基础过滤器
│   │   ├── user_filters.py       # 用户过滤
│   │   ├── inbound_order_filters.py  # 入库订单过滤
│   │   └── ...
│   ├── middlewares/              # 中间件
│   │   ├── error_handler.py      # 错误处理
│   │   └── request_log.py        # 请求日志
│   ├── models/                   # 数据模型层
│   │   ├── base.py               # 基础模型
│   │   ├── user_models.py        # 用户模型
│   │   ├── company_models.py     # 公司模型
│   │   ├── inbound_order_models.py    # 入库订单模型
│   │   ├── orders_models.py      # 订单模型 (外部系统)
│   │   ├── subscribe_models.py   # 订阅模型
│   │   ├── sequence_models.py    # 序列号模型
│   │   └── common_models.py      # 通用模型
│   ├── schemas/                  # Pydantic 模型
│   │   ├── base.py               # 基础响应模型
│   │   ├── login_schemas.py      # 登录相关
│   │   ├── user_schemas.py       # 用户相关
│   │   ├── inbound_order_schemas.py   # 入库订单相关
│   │   └── ...
│   ├── services/                 # 业务逻辑层
│   │   ├── login_service.py      # 登录服务
│   │   ├── user_service.py       # 用户服务
│   │   ├── inbound_order_service.py   # 入库订单服务
│   │   ├── shipping_services.py  # 发货服务
│   │   └── order_service.py      # 订单服务
│   ├── tasks/                    # 后台任务
│   ├── utils/                    # 工具函数
│   └── main.py                   # 应用入口
├── migrations/                   # 数据库迁移文件
├── tests/                        # 测试文件
├── docs/                         # 文档目录
├── deploy_scripts/               # 部署脚本
├── scripts/                      # 工具脚本
├── logs/                         # 日志文件
├── pyproject.toml               # 项目配置
├── local_settings.py            # 本地配置
├── gunicorn.conf.py             # Gunicorn 配置
└── start.sh                     # 启动脚本
```

## 🗄️ 数据库设计

### 核心表结构

#### 1. 用户与公司管理

- **cert_users**: 用户信息表
- **cert_companies**: 公司信息表
- **cert_sequence**: 序列号生成表

#### 2. 入库订单管理

- **cert_inbound_order**: 入库单主表
- **cert_inbound_order_detail**: 入库单明细表 (任务分配)

#### 3. 发货管理 (核心业务)

- **cert_shipping_waybill**: 发货面单表
- **cert_waybill_product**: 面单商品表 (合并证书绑定功能)

#### 4. 外部订单数据

- **orders_order**: 订单主表 (外部系统)
- **orders_orderitems**: 订单明细表 (外部系统)

#### 5. 订阅与通用数据

- **cert_shipping_subscribe**: 发货订阅表
- **cert_live_authors**: 达人管理表
- **cert_shops**: 店铺管理表

### 关键业务模型

#### WaybillProduct (面单商品模型)

这是系统的核心模型，合并了商品信息和证书绑定功能：

```python
class WaybillProduct(BasicFieldsModel):
    # 面单关联
    waybill = ForeignKeyField("ShippingWaybill")

    # 订单信息
    order_id = CharField(max_length=128)  # 订单号
    outer_oi_id = CharField(max_length=128)  # 外部订单项ID
    sku_id = CharField(max_length=128)  # SKU ID
    quantity = IntField(default=1)  # 数量

    # 证书绑定信息
    cert_sn_code = CharField(max_length=128, unique=True)  # 证书SN码
    cert_link = CharField(max_length=500)  # 证书链接
    cert_type = IntField()  # 证书类型 (1:一物一证, 2:随机发证)
    product_sn_code = CharField(max_length=128)  # 商品SN码
    bind_status = IntField(default=1)  # 绑定状态 (1:未绑定, 2:已绑定)
    bind_time = DatetimeField()  # 绑定时间
```

### 数据库索引策略

系统针对高频查询场景设计了完善的索引：

- 快递单号查询: `(waybill_code, company_id)`
- 订单号查询: `(order_id, company_id)`
- 证书号查询: `cert_sn_code` (唯一索引)
- 商品SN查询: `product_sn_code`

## 🔧 核心功能模块

### 1. 用户认证系统

- **JWT 认证**: 访问令牌 + 刷新令牌机制
- **多种登录方式**: 用户名/手机号密码登录、短信验证码登录
- **权限管理**: 基于角色的权限控制
- **多公司支持**: SaaS 架构，数据隔离

### 2. 入库订单管理

- **入库单创建**: 支持批量商品入库
- **任务分配**: 将入库单分配给发货人员
- **状态跟踪**: 待扫描 → 待发货 → 发货中 → 已完成

### 3. 发货管理系统

- **面单管理**: 创建和管理发货面单
- **证书绑定**: 支持一物一证和随机发证两种模式
- **快速查询**: 支持多种查询方式 (面单号、订单号、证书号等)

### 4. 订单查询系统

- **跨数据库查询**: 使用原生SQL连接多个数据库
- **性能优化**: 通过LEFT JOIN优化查询性能
- **数据聚合**: 整合订单、商品、店铺、达人等信息

## 🚀 部署架构

### 生产环境部署

- **Web服务器**: Gunicorn (多进程) + Uvicorn (ASGI Worker)
- **数据库**: PostgreSQL (主库) + Redis (缓存)
- **日志管理**: Loguru + 文件轮转
- **进程管理**: Supervisor (可选)

### 配置管理

- **环境配置**: 通过 `local_settings.py` 管理不同环境配置
- **数据库配置**: 支持多数据库连接
- **Redis配置**: 连接池管理，支持集群

## 📊 性能特性

### 1. 异步架构

- 全异步 FastAPI + Tortoise ORM
- 支持高并发请求处理
- 非阻塞 I/O 操作

### 2. 数据库优化

- 合理的索引设计
- 查询优化 (使用原生SQL处理复杂查询)
- 连接池管理

### 3. 缓存策略

- Redis 缓存热点数据
- 短信验证码缓存
- 会话管理

## 🔍 业务特色

### 1. 证书绑定系统

- **灵活的绑定模式**: 支持一物一证和随机发证
- **快速查询**: 通过多种编码快速定位商品和证书
- **状态跟踪**: 完整的绑定状态管理

### 2. 多公司架构

- **数据隔离**: 每个公司的数据完全隔离
- **权限控制**: 基于公司的权限管理
- **可扩展性**: 支持无限公司接入

### 3. 工作流管理

- **任务分配**: 入库单自动分配给发货人员
- **状态流转**: 完整的业务状态管理
- **操作日志**: 完整的操作记录

## 📈 扩展性设计

### 1. 模块化架构

- 清晰的分层架构
- 松耦合的模块设计
- 易于扩展新功能

### 2. 数据库设计

- 预留扩展字段
- 灵活的关联关系
- 支持业务变更

### 3. API设计

- RESTful API 规范
- 统一的响应格式
- 完整的文档支持

## 🛠️ 开发工具

### 1. 代码质量

- **Ruff**: 代码格式化和静态检查
- **Pytest**: 单元测试框架
- **类型提示**: 完整的类型注解

### 2. 数据库工具

- **Aerich**: 数据库迁移管理
- **inspectdb**: 数据库结构检查工具

### 3. 部署工具

- **部署脚本**: 自动化部署脚本
- **性能测试**: 内置性能测试工具

---

*本文档基于项目代码分析生成，详细描述了 ZhuLinks 证书绑定系统的完整架构和功能特性。*
