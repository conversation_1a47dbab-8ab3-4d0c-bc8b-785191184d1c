#!/usr/bin/env python3
"""
性能测试脚本
测试Gunicorn + Uvicorn部署的性能
"""

import asyncio
import time
import aiohttp
import statistics
from concurrent.futures import ThreadPoolExecutor

async def test_endpoint(session, url, semaphore):
    """测试单个请求"""
    async with semaphore:
        start_time = time.time()
        try:
            async with session.get(url) as response:
                await response.text()
                end_time = time.time()
                return {
                    'status': response.status,
                    'time': end_time - start_time,
                    'success': True
                }
        except Exception as e:
            end_time = time.time()
            return {
                'status': 0,
                'time': end_time - start_time,
                'success': False,
                'error': str(e)
            }

async def run_performance_test(base_url="http://localhost:8000", 
                             concurrent_requests=50, 
                             total_requests=1000):
    """运行性能测试"""
    
    print(f"=== 性能测试开始 ===")
    print(f"目标URL: {base_url}")
    print(f"并发请求数: {concurrent_requests}")
    print(f"总请求数: {total_requests}")
    print()
    
    # 创建信号量限制并发数
    semaphore = asyncio.Semaphore(concurrent_requests)
    
    # 测试端点
    test_urls = [
        f"{base_url}/health",
        f"{base_url}/api/v1/docs",
    ]
    
    results = []
    
    async with aiohttp.ClientSession() as session:
        start_time = time.time()
        
        # 创建任务
        tasks = []
        for i in range(total_requests):
            url = test_urls[i % len(test_urls)]
            task = test_endpoint(session, url, semaphore)
            tasks.append(task)
        
        # 执行所有任务
        results = await asyncio.gather(*tasks)
        
        end_time = time.time()
    
    # 分析结果
    successful_requests = [r for r in results if r['success']]
    failed_requests = [r for r in results if not r['success']]
    
    if successful_requests:
        response_times = [r['time'] for r in successful_requests]
        
        print(f"=== 测试结果 ===")
        print(f"总耗时: {end_time - start_time:.2f}秒")
        print(f"成功请求: {len(successful_requests)}")
        print(f"失败请求: {len(failed_requests)}")
        print(f"成功率: {len(successful_requests)/total_requests*100:.2f}%")
        print(f"QPS: {len(successful_requests)/(end_time - start_time):.2f}")
        print()
        print(f"响应时间统计:")
        print(f"  平均: {statistics.mean(response_times)*1000:.2f}ms")
        print(f"  中位数: {statistics.median(response_times)*1000:.2f}ms")
        print(f"  最小: {min(response_times)*1000:.2f}ms")
        print(f"  最大: {max(response_times)*1000:.2f}ms")
        
        if len(response_times) > 1:
            print(f"  标准差: {statistics.stdev(response_times)*1000:.2f}ms")
    
    if failed_requests:
        print(f"\n=== 失败请求详情 ===")
        for i, req in enumerate(failed_requests[:5]):  # 只显示前5个错误
            print(f"错误 {i+1}: {req.get('error', 'Unknown error')}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='性能测试脚本')
    parser.add_argument('--url', default='http://localhost:8000', 
                       help='测试URL (默认: http://localhost:8000)')
    parser.add_argument('--concurrent', type=int, default=50,
                       help='并发请求数 (默认: 50)')
    parser.add_argument('--total', type=int, default=1000,
                       help='总请求数 (默认: 1000)')
    
    args = parser.parse_args()
    
    # 运行测试
    asyncio.run(run_performance_test(
        base_url=args.url,
        concurrent_requests=args.concurrent,
        total_requests=args.total
    ))

if __name__ == "__main__":
    main()
