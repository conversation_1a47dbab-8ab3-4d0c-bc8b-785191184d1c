#!/bin/bash

# 安装部署依赖脚本

set -e

echo "=== 安装 Gunicorn + Uvicorn 部署依赖 ==="

# 检查Python版本
python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
echo "Python版本: $python_version"

if [[ $(echo "$python_version >= 3.8" | bc -l) -eq 0 ]]; then
    echo "错误: 需要Python 3.8或更高版本"
    exit 1
fi

# 升级pip
echo "升级pip..."
python3 -m pip install --upgrade pip

# 安装核心依赖
echo "安装Gunicorn和Uvicorn..."
pip install gunicorn uvicorn[standard]

# 安装可选的性能优化依赖
echo "安装性能优化依赖..."
pip install httptools uvloop

# 如果存在requirements.txt，安装项目依赖
if [ -f "requirements.txt" ]; then
    echo "安装项目依赖..."
    pip install -r requirements.txt
elif [ -f "pyproject.toml" ]; then
    echo "安装项目依赖..."
    pip install -e .
fi

echo "依赖安装完成！"

# 显示版本信息
echo ""
echo "=== 版本信息 ==="
echo "Gunicorn: $(gunicorn --version)"
echo "Uvicorn: $(uvicorn --version)"
echo ""
echo "=== 启动命令 ==="
echo "开发环境: python uvicorn_start.py"
echo "生产环境: ./start.sh"
echo "或者: gunicorn -c gunicorn.conf.py app.main:app"
