[unix_http_server]
file=/tmp/supervisor.sock
chmod=0700

[supervisord]
logfile=/var/log/supervisor/supervisord.log
logfile_maxbytes=50MB
logfile_backups=10
loglevel=info
pidfile=/var/run/supervisord.pid
nodaemon=false
minfds=1024
minprocs=200

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///tmp/supervisor.sock

[program:zhulinks-api]
command=gunicorn -c gunicorn.conf.py app.main:app
directory=/path/to/your/project  ; 修改为您的项目路径
user=www-data                    ; 修改为合适的用户
autostart=true
autorestart=true
startretries=3
redirect_stderr=true
stdout_logfile=/var/log/supervisor/zhulinks-api.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
environment=PYTHONUNBUFFERED="1",PYTHONDONTWRITEBYTECODE="1"
stopsignal=TERM
stopwaitsecs=30
killasgroup=true
stopasgroup=true
priority=999
