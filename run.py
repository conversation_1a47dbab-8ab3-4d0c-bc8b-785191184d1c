#!/usr/bin/env python3
"""
生产环境启动脚本
优化了资源管理和信号处理
"""

import asyncio
import signal
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import uvicorn  # noqa: E402

from app.core.config import settings  # noqa: E402


class ProductionServer:
    """生产环境服务器管理器"""

    def __init__(self):
        self.server = None
        self.should_exit = False

    def setup_signal_handlers(self):
        """设置信号处理器"""

        def signal_handler(signum, frame):
            print(f"\n收到信号 {signum}，开始优雅关闭...")
            self.should_exit = True

        # 注册信号处理器
        signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
        signal.signal(signal.SIGTERM, signal_handler)  # 终止信号

        # 在 Unix 系统上注册额外的信号
        if hasattr(signal, "SIGHUP"):
            signal.signal(signal.SIGHUP, signal_handler)

    async def cleanup_resources(self):
        """清理应用资源"""
        try:
            print("开始清理应用资源...")

            # 导入清理函数
            from app.core.logging import cleanup_logging, force_cleanup_multiprocessing
            from app.core.redis import RedisClient

            # 清理 Redis 连接
            try:
                await RedisClient.close_redis_pool()
            except Exception as e:
                print(f"清理 Redis 连接时出错: {e}")

            # 清理日志系统
            try:
                cleanup_logging()
            except Exception as e:
                print(f"清理日志系统时出错: {e}")

            # 清理多进程资源 - 在直接运行时才执行
            try:
                # 只在直接运行run.py时执行多进程清理，gunicorn环境下跳过
                import os
                if not os.environ.get('GUNICORN_WORKER'):
                    force_cleanup_multiprocessing()
                else:
                    print("在gunicorn环境中，跳过多进程资源清理")
            except Exception as e:
                print(f"清理多进程资源时出错: {e}")

            print("资源清理完成")

        except Exception as e:
            print(f"资源清理过程中出错: {e}")

    def run(self):
        """运行生产环境服务器"""
        # 设置信号处理器
        self.setup_signal_handlers()
        port = 8001
        try:
            print("启动生产环境服务器...")
            print(f"环境: {'开发' if settings.DEBUG else '生产'}")
            print("监听地址: http://0.0.0.0:{port}")
            print(f"API 文档: http://0.0.0.0:{port}{settings.API_V1_STR}/docs")

            # 启动服务器
            uvicorn.run(
                "app.main:app",
                host="0.0.0.0",
                port=port,
                log_level="info" if not settings.DEBUG else "debug",
                access_log=True,
                use_colors=False,  # 生产环境禁用颜色
                server_header=False,  # 隐藏服务器头信息
                date_header=True,
                reload=True if settings.DEBUG else False,
            )
        except KeyboardInterrupt:
            print("\n收到中断信号，正在关闭服务器...")
        except Exception as e:
            print(f"服务器运行时出错: {e}")
        finally:
            # 清理资源
            try:
                asyncio.run(self.cleanup_resources())
            except Exception as e:
                print(f"最终清理时出错: {e}")

            print("服务器已关闭")


def main():
    """主函数"""
    # 检查生产环境配置
    if not settings.DEBUG:
        print("运行在生产环境模式")

        # 生产环境安全检查
        if settings.JWT_SECRET_KEY == "your-jwt-secret-key-here":
            print("警告: 请在生产环境中更改默认的 JWT 密钥!")

        if "*" in settings.BACKEND_CORS_ORIGINS:
            print("警告: 生产环境不建议使用通配符 CORS 设置!")

    # 创建并运行服务器
    server = ProductionServer()
    server.run()


if __name__ == "__main__":
    main()
